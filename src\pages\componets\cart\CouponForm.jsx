import useCouponForm from "../../../hooks/cart/useCouponForm.js";

export default function CouponForm(props){

    const {formik} = useCouponForm(props);

    return (
        <form onSubmit={formik.handleSubmit}>
            <div className="col d-flex gap-3 mt-4" >
                <input
                    type="text"
                    className="cart_sum_input"
                    placeholder="Enter a code"
                    id="couponCode"
                    {...formik.getFieldProps('couponCode')}
                />
                <button type="submit" className="cart_sum_btn">
                    Apply
                </button>
            </div>
            {formik.touched.couponCode && <small className="main_footer_copy text-danger">{formik.errors.couponCode}</small>}
        </form>
    )
}