import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getHelpCategory} from "../../api/help/getHelpCategory.js";

export const useHelpCategory = (props) => {
    const { data: category, loading: categoryLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['help-categories', {...props}],
            queryFn: () => getHelpCategory(props),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { category, categoryLoading};
};