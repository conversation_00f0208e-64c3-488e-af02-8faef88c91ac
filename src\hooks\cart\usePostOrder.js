import {useQuery, useQueryClient} from "react-query";
import {showError, showSuccess, useQueryFix} from "../../vbrae-utils/index.js";
import {postOrder} from "../../api/cart/postOrder.js";
import {useNavigate} from "react-router-dom";

export const usePostOrder = () => {

    const queryClient = useQueryClient();
    const navigate = useNavigate();

    const { loading: orderLoading, refetch: orderRefetch } = useQueryFix({
        query: useQuery({
            queryKey: ['add-order'],
            queryFn: () => postOrder(),
            onError: showError,
            refetchOnWindowFocus: false,
            onSuccess: ()=> {
                queryClient.invalidateQueries('get-cart').finally()
                showSuccess("Order created successfully", ()=> navigate("/"));

            },
            enabled: false
        }),
        transform: (data) => data,
    });

    return { orderLoading, orderRefetch };
};