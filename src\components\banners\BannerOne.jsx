import SaleTimer from "../SaleTimer.jsx";
import {Link} from "react-router-dom";
import {getIcon} from "../../vbrae-utils/lib/getIcons.jsx";

export default function BannerOne({link, images, endTime, category, couponCode, discountPercent, existingPrice, logoImage, shortSummary, tag, title}){

    const [p1, p2] = existingPrice.split(".");

    return (
        <Link to={link} className="col banner_item1_con mb-3 mb-lg-0 d-block" target="_blank"
              style={{backgroundImage: `url(${images.desktop})`}}>
            <div className="banner_item1_cont">
                <div className="d-flex justify-content-between align-items-center">
                    <div className="banner_timing d-flex align-items-center mb-3">
                        <SaleTimer expiryTimestamp={endTime}/>
                        {getIcon(category)}
                    </div>

                    <img
                        src={logoImage}
                        alt=""
                        className="banner_logo d-none d-md-block"
                    />
                </div>

                <div className="mb-3">
                    <h5 className="banner_title">{title}</h5>
                    <span className="banner_title tag">
                            {tag}
                          </span>
                </div>

                <div className="col col-md-10 col-lg-8 mb-3">
                    <p className="banner_text">
                        {shortSummary}
                    </p>
                </div>

                <div className="d-flex justify-content-between align-items-end">
                    <div className="d-md-flex align-items-center">
                        <div className="banner_coupon d-flex align-items-center mb-2 mb-md-0">
                            <span> {discountPercent}%</span>
                            <p className="ms-1 mb-0">
                                <span>off </span>
                                with <br/> code
                            </p>
                        </div>
                        <div
                            className="banner_coupon_code d-flex align-items-center"
                            style={{
                                backgroundImage:
                                    "url(./assets/images/vouche_lg.svg)",
                            }}>
                            <p className="mb-0">{couponCode}</p>
                            <span className="d-flex justify-content-center align-items-center ms-2">
                                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24"><path
                                    fill="currentColor"
                                    d="M4.8 21.57L7.222 24L19.2 12L7.222 0L4.8 2.43L14.347 12z"></path></svg>
                              </span>
                        </div>
                    </div>

                    <div className="text-end">
                        <p className="banner_price_sm mb-0">${p1}{p2 && <>.{p2}</>}</p>
                        <p className="banner_price mb-0">
                            ${p1}{p2 && <span>.{p2}</span>}
                        </p>
                    </div>
                </div>
            </div>
        </Link>
    )
}