import {useEffect, useState} from "react";
import Spinner from "../common/Spinner.jsx";
import GameItem from "../../pages/componets/GameItem.jsx";
import EmptyGameItem from "../../pages/componets/EmptyGameItem.jsx";
import {useBestSellers} from "../../hooks/offers/useBestSellers.js";

export default function BestSellerOffers({sellerId, setShopCounts}){

    const [selectedRegion, setSelectedRegion] = useState("");
    const [selectedLanguage, setSelectedLanguage] = useState("");
    const [selectedGenre, setSelectedGenre] = useState("");
    const [prices, setPrices] = useState([]);
    const [selectedBoxes, setSelectedBoxes] = useState({});
    const [filledCards, setFilledCards] = useState([]);

    const {offers, offersLoading} = useBestSellers({sellerId,query: ''})

    const fillGrid = () => {
        const container = document.querySelector(".game_list_con");
        const containerWidth = container.clientWidth; // Get container width
        const itemsPerRow = Math.floor(containerWidth / 250); // Calculate items that can fit

        const totalItems = Math.ceil(offers.length / itemsPerRow) * itemsPerRow; // Calculate total items needed
        const placeholders = totalItems - offers.length; // Calculate number of placeholders needed
        const filled = [...offers, ...Array(placeholders).fill(null)]; // Create filled array

        setFilledCards(filled);
    };

    useEffect(() => {

        if(!offers) return;

        fillGrid();
        window.addEventListener("resize", fillGrid); // Refill on resize
        setShopCounts(prev=> ({...prev, bestOffers: offers.length}))
        return () => window.removeEventListener("resize", fillGrid);
    }, [offers]);

    const resetFilters = () => {
        setSelectedRegion("");
        setSelectedLanguage("");
        setSelectedGenre("");
        setPrices([]);
        setSelectedBoxes({})
    }

    return (
        <div className="col d-flex gap-4 position-relative">
            <div className="w-100">
                <div
                    className="col d-xl-flex flex-row-reverse align-items-start align-items-xl-center gap-3 gap-xl-4 mb-4">
                    <div className="col d-flex gap-2 overflow-auto hide_scroll">
                        {Object.keys(selectedBoxes).length > 0 &&
                            <span className="cate_filter_tag" onClick={resetFilters}>
                              Clear all
                            <svg
                                className="ms-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                              <path
                                  fill="currentColor"
                                  d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                              />
                            </svg>
                          </span>}

                        {Object.entries(selectedBoxes).map(([key, value], index) => {
                            if (key === "delivery") {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState.delivery;
                                              return newState;
                                          })}
                                          key={index}>
                                    {key} {value.join('/')}
                                        <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em"
                                             height="1em" viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                );
                            } else if (key === 'stock[gt]') {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState[`stock[gt]`];
                                              return newState;
                                          })}
                                          key={index}>
                                    In stock
                                <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                     viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                );
                            } else if (key === 'instantDelivery' && value[0]) {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState.instantDelivery;
                                              return newState;
                                          })}
                                          key={index}>
                                    Pre Order
                                <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                     viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                )
                            } else if (key === 'region') {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState.region;
                                              return newState;
                                          })}
                                          key={index}>
                                    {selectedBoxes?.region[0]}
                                        <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em"
                                             height="1em"
                                             viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                )
                            } else if (key === 'category') {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState.category;
                                              return newState;
                                          })}
                                          key={index}>
                                    {selectedBoxes?.category[0]}
                                        <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em"
                                             height="1em"
                                             viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                )
                            }
                            return null;
                        })}

                        {prices[0] &&
                            <span className="cate_filter_tag" onClick={() => setPrices([undefined, prices[1]])}>
                      Min: ${prices[0]}
                                <svg
                                    className="ms-1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                        {prices[1] &&
                            <span className="cate_filter_tag" onClick={() => setPrices([prices[0], undefined])}>
                      Max: ${prices[1]}
                                <svg
                                    className="ms-1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                        {selectedRegion && <span className="cate_filter_tag" onClick={() => setSelectedRegion("")}>
                      {selectedRegion}
                            <svg
                                className="ms-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                        {selectedLanguage && <span className="cate_filter_tag" onClick={() => setSelectedLanguage("")}>
                      {selectedLanguage}
                            <svg
                                className="ms-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                        {selectedGenre && <span className="cate_filter_tag" onClick={() => setSelectedGenre("")}>
                      {selectedGenre}
                            <svg
                                className="ms-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                    </div>

                    <div className="col d-none d-flex gap-2 overflow-auto">
                            <span className="cate_filter_head text-nowrap my-auto">
                              34 ITEMS
                            </span>

                        <span className="cate_filter_tag">
                              Clear all
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                        <span className="cate_filter_tag">
                              Switzerland
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                        <span className="cate_filter_tag">
                              Instant
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                        <span className="cate_filter_tag active">
                              24H
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                        <span className="cate_filter_tag">
                              Max: $1000
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                    </div>
                </div>

                {offersLoading || !offers ? <Spinner/> : <>
                    <div className="col game_list_con row justify-content-start g-3 mb-4">
                        {filledCards.map((card, index) => (
                            <>
                                {card !== null ? (
                                    <GameItem key={index} {...card} />
                                ) : (
                                    <EmptyGameItem/>
                                )}
                            </>
                        ))}
                    </div>
                </>}

            </div>
        </div>
    )
}