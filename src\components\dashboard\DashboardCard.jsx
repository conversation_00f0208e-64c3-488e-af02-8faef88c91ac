export default function DashboardCard({bgImage, label, value, icon}){

    const newLabel = label === "Offer Count"

    return (
        <div className="col">
            <div
                className="col dash_cont d-flex flex-column justify-content-end position-relative"
                style={{
                    backgroundImage: `url(./assets/images/${bgImage})`,
                }}>
                    <span className="dash_cont_icon position-absolute">
                      {icon}
                    </span>
                <p className="dash_cont_num mb-2">{newLabel ? '' : '$'}{value}</p>
                <p className="dash_cont_head">{label}</p>
            </div>
        </div>
    )
}