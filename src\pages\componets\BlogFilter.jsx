import {Link} from "react-router-dom";
import Spinner from "../../components/common/Spinner.jsx";
import {useBlogFilters} from "../../hooks/blogs/useBlogFilters.js";

export default function BlogFilter({setActiveFilter, activeFilter}) {

  const {filters, loading} = useBlogFilters();

  if(!filters || loading) return <Spinner />

  return (
    <>
      <div className="col col-lg-3 col-xl-2 d-none d-lg-block">
        <div className="col mb-4">
          <p className="blog_head mb-2">main Category</p>
          <div className="col">
            <Link onClick={() => setActiveFilter({tag: "", category: ""})}>
              <p className={`blog_cate_text mb-2`}>All</p>
            </Link>
            {filters.categories.map((filter) => (
                <Link key={filter._id} onClick={() => setActiveFilter(prevState => ({...prevState, category: filter.categoryId}))}>
                  <p className={`blog_cate_text mb-2 ${filter.categoryId === activeFilter.category ? 'active' : ''}`}>{filter.categoryName}</p>
                </Link>
            ))}
          </div>
        </div>
        <div className="col mb-4">
          <p className="blog_head mb-2">More Tags</p>
          <div className="col">
            {filters.tags.map((keyword) => (
                <Link key={keyword} onClick={() => setActiveFilter(prevState => ({...prevState, tag: keyword}))}>
                  <p className={`blog_cate_text mb-2 ${keyword === activeFilter.tag ? 'active' : ''}`}>#{keyword}</p>
                </Link>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
