import { useEffect, useState } from 'react';

export function useQueryFix({ query, transform }) {
  const { isLoading, error, data, refetch, isFetching } = query;

  const [localData, setLocalData] = useState(
      data ? transform(data) : undefined
  );
  const [localLoading, setLocalLoading] = useState(isLoading);

  useEffect(() => {
    if (isLoading) {
      setLocalLoading(true);
    }

    if (error && localLoading) {
      setLocalLoading(false);
    }

    if (data) {
      setLocalData(transform(data));
      setLocalLoading(false);
    }
  }, [isLoading, error, data, transform, localLoading]);

  const handleRefetch = async () => {
    setLocalData(true)
    await refetch();
    setLocalData(false)
  };

  return {
    data: localData,
    loading: localLoading,
    error,
    refetch: handleRefetch,
    isFetching
  };
}
