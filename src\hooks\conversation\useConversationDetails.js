import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getConversationById} from "../../api/conversation/getConversationById.js";
import {useSearchParams} from "react-router-dom";

export const useConversationDetails = () => {

    const [searchParams] = useSearchParams();
    const conversationId = searchParams.get('conversationId');

    const { data: conversation, loading: conversationLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['conversations', conversationId],
            queryFn: () => getConversationById({conversationId}),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: !!conversationId,
        }),
        transform: (data) => data,
    });

    return { conversation, conversationLoading};
};