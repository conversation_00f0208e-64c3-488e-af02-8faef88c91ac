export const convertToDays = (dateString) => {
    const date = new Date(dateString);
    const options= { day: "2-digit", month: "long", year: "numeric" };
    return date.toLocaleDateString("en-US", options);
};

export function formatDateTime(isoString) {
    const date = new Date(isoString);

    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year = date.getFullYear();

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    const ampm = hours >= 12 ? 'pm' : 'am';

    hours = hours % 12 || 12;

    return [`${day}/${month}/${year}`, `${hours}:${minutes}:${seconds} ${ampm}`];
}
