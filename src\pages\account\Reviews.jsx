import {Link} from "react-router-dom";
import Pagination from "../componets/utility/Pagination";
import AccountSideBar from "../componets/account/AccountSideBar";
import MainHeader from "../componets/MainHeader";
import MainFooter from "../componets/MainFooter";
import ReviewRow from "../../components/reviews/ReviewRow.jsx";
import ReviewMobileRow from "../../components/reviews/ReviewMobileRow.jsx";
import {useReviews} from "../../hooks/rating/useReviews.js";
import Spinner from "../../components/common/Spinner.jsx";
import ReplyModal from "../componets/modals/ReplyModal.jsx";
import {useState} from "react";
import {getUserId} from "../../vbrae-utils/index.js";

export default function Reviews() {
  const breadCrums = [
    {
      title: "Reviews",
      url: "/account/reviews",
    },
  ];

  const breadCrumsList = breadCrums?.map((item, index) => {
    return (
        <Link to={item.url} key={index}>
          <p className="crumb_link">/ {item.title}</p>
        </Link>
    );
  });

  return (
    <>
      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={"reviews"} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadCrums}
            activeLink={"reviews"}
          />

          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <div className=" d-lg-none justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-2">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                {breadCrumsList}
              </div>
            </div>
            <RenderReviews/>

            <div className="col d-lg-none mt-4">
              <MainFooter/>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

const initialState = {
  reviewId: "",
  offerId: "",
  isOpen: false,
  comment: ""
}

const RenderReviews = () => {

  const {reviews, reviewLoading} = useReviews({userId: getUserId()});
  const [page, setPage] = useState(1);
  const [data, setData] = useState(initialState)

  if(!reviews || reviewLoading) return <Spinner />;

  const ratingStats = [
    {_id: 1, label: "Your rating", value: `${reviews.averageRating} / 5`},
    {_id: 1, label: "Total Reviews", value: reviews.ratings.length},
    {_id: 1, label: "Positive Feedback", value: "97%"},
    {_id: 1, label: "Followers", value: reviews.followersCount},
  ]

  return (
      <>
        <ReplyModal onClose={()=> setData(initialState)} data={data} />
        <div className="acct_cont ">
          <div className="col">
            <div className="col row row-cols-2 row-cols-lg-4 g-2 justify-content-between mb-4">
              {ratingStats.map(({label, value}, index) => (
                  <div className="col" key={index}>
                    <div className="col acct_off_cont">
                      <p className="acct_off_num mb-2">{value}</p>
                      <p className="acct_off_head">{label}</p>
                    </div>
                  </div>
              ))}
            </div>

            <hr className="acct_fil_line mb-0"/>

            {/* Mobile Table */}
            <div className="col d-lg-none mb-4">
              {reviews.ratings.map((item, index) => <ReviewMobileRow key={item._id} setData={setData} index={index} {...item} />)}
            </div>

            {/* Desktop Table */}
            <div className="table-responsive d-none d-lg-block">
              <table className="table table-borderless acct_table mb-3">
                <thead>
                <tr>
                  <th className="acct_table_head">ID</th>
                  <th className="acct_table_head">Order Id</th>
                  <th className="acct_table_head">Buyer</th>
                  <th className="acct_table_head">Rating</th>
                  <th className="acct_table_head">Latest Reviews</th>
                  <th className="acct_table_head">Product</th>
                  <th className="acct_table_head">Date</th>
                  <th className="acct_table_head"></th>
                </tr>
                </thead>
                <tbody>
                {reviews.ratings.map((item, index) => <ReviewRow key={item._id} setData={setData}
                                                                 index={index} {...item} />)}
                </tbody>
              </table>
            </div>

            <div className="col d-flex justify-content-center">
              <Pagination
                  totalPages={reviews.totalPages}
                  currentPage={page}
                  pageClick={(page) => setPage(page)}
                  nextPage={() => setPage(page + 1)}
                  prevPage={() => setPage(page - 1)}/>
            </div>
          </div>
        </div>
      </>
  )
}
