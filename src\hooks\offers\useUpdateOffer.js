import {useQuery, useQueryClient} from 'react-query';
import {showError, showSuccess, useQueryFix} from "../../vbrae-utils/index.js";
import {updateOffer} from "../../api/offers/updateOffer.js";


export default function useUpdateOffer({onSuccess, ...props}) {
    const queryClient = useQueryClient();
    const { refetch:updateRefetch, loading:updateLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['update-product', props._id],
            queryFn: () => updateOffer(props),
            onError: showError,
            onSuccess: () => queryClient.invalidateQueries(['seller-offers']).finally(()=> {
                showSuccess("Offer Updated Successfully");
                onSuccess?.();
            }),
            enabled: false,
        }),
        transform: (data) => data,
    });
    return { updateRefetch, updateLoading };
}