import {Link} from "react-router-dom";
import {useOrders} from "../hooks/orders/useOrders.js";
import {convertToDays} from "../vbrae-utils/lib/time.js";
import Spinner from "./common/Spinner.jsx";

export default function LatestPurchase(){

    const {orders, orderLoading} = useOrders();

    if(!orders ||orderLoading) {
        return <Spinner />
    }

    if(orders.length === 0){
        return <div className="cart_item_name text-center mt-5">Nothing here yet!</div>
    }

    return (
        <>
            <div className="col">
                {orders.map((orderObj) => {
                    return orderObj.items.map((order) => {
                        const [p1, p2] = order.offer.customerPays?.toString().split(".") || [];
                        return (
                            <div className="rs_item1_cont mb-3" key={order._id}>
                                <div className="d-flex justify-content-between align-items-center mb-2">
                                    <p className="rs_item1_type">Psc•Dlc</p>
                                    <p className="rs_item1_time">{convertToDays(orderObj.createdAt)}</p>
                                </div>
                                <p className="rs_item1_text">{order.offer.name}</p>
                                <div className="d-flex align-items-end mb-2">
                                    <p className="banner_sm_price me-2 mb-0">
                                        ${p1}
                                        {p2 && <span>.{p2}</span>}
                                    </p>
                                    <p className="banner_sm_price_sm mb-0">
                                        ${order.offer.template.price}
                                    </p>
                                </div>
                                <Link to={`/shop-details/${order.offer.seller.shop._id}?seller=${order.offer.seller._id}`}>
                                    <div className="d-flex gap-2">
                                        <img
                                            src={order.offer.seller.avatar || window.location.origin + "/assets/images/user.png"}
                                            alt="Seller"
                                            className="rs_item1_img"
                                        />
                                        <div className="col">
                                            <p className="rs_item1_img_text_sm">{order.offer.seller.name}</p>
                                            <p className="rs_item1_img_text">{order.offer.seller.shop.shopName}</p>
                                        </div>
                                    </div>
                                </Link>
                            </div>
                        );
                    });
                })}
            </div>
        </>
    )
}