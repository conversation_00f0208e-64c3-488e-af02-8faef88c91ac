/* eslint-disable react/prop-types */
import {useEffect, useState} from "react";

export default function CustomAccordion({ isActive, trigger, content, disabled=false }) {
  const [isOpen, setIsOpen] = useState(isActive ?? false);

  useEffect(() => {
      setIsOpen(!!isActive);
  }, [isActive])

  function ToggleAccordion() {
    if(disabled) return;
    setIsOpen(!isOpen);
  }

  return (
    <>
      <div className="col-auto" role="button" onClick={() => ToggleAccordion()}>
        {trigger(isOpen)}
      </div>

      {isOpen && content}
    </>
  );
}
