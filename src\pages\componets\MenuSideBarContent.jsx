/* eslint-disable react/prop-types */
import { Link } from "react-router-dom";
import {useCategories} from "../../hooks/categories/useCategories.js";
import {getIcon} from "../../vbrae-utils/lib/getIcons.jsx";
import Spinner from "../../components/common/Spinner.jsx";
// import CustomDropdown from "./utility/CustomDropdown";

export default function MenuSideBarContent({ isShort, activeLink }) {
  const {categories} = useCategories();

  if(!categories) {
    return (
        <Spinner />
    )
  }

  return (
      <>
        <div className={(isShort ? "col-auto" : "col") + " align-items-stretch"}>
        <div
          className={
            "mb-4 d-flex flex-column " + (isShort ? "" : "align-items-stretch")
          }>
          {categories.map((category, index) => (
              <Link to={`/${category.slug}/${category._id}`} className="d-block mb-1" key={index}>
                <p
                    className={
                        (activeLink === category.slug && "active") +
                        " menu_sidebar_link d-flex align-items-center gap-3 " +
                        (isShort ? " short justify-content-center" : "")
                    }>
                  {getIcon(category.slug)}
                  {!isShort ? category.name : ""}
                </p>
              </Link>
          ))}
        </div>

        <hr className="menu_sidebar_line" />

        <div className={"my-4 position-relative " + (isShort && "d-none")}>
          {/* {isShort && (
            <div className="col d-none d-lg-block">
              <CustomDropdown
                trigger={() => (
                  <p
                    className="menu_sidebar_link d-flex justify-content-center"
                    role="button">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20px"
                      height="20px"
                      viewBox="0 0 24 24">
                      <path
                        fill="none"
                        stroke="currentColor"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 12a1 1 0 1 0 2 0a1 1 0 1 0-2 0m7 0a1 1 0 1 0 2 0a1 1 0 1 0-2 0m7 0a1 1 0 1 0 2 0a1 1 0 1 0-2 0"></path>
                    </svg>
                  </p>
                )}
                content={
                  <div className="header_drop_cont start-0 position-absolute">
                    <Link
                      to={"/affiliate"}
                      className="col-12 d-inline-block mb-3 px-3">
                      <p
                        className={
                          "menu_sidebar_link2 " +
                          (activeLink == "affiliate" && "active")
                        }>
                        Affiliate
                      </p>
                    </Link>
                    <Link
                      to={"/about"}
                      className="col-12 d-inline-block mb-3 px-3">
                      <p
                        className={
                          "menu_sidebar_link2 " +
                          (activeLink == "about" && "active")
                        }>
                        About Us
                      </p>
                    </Link>
                    <Link
                      to={"/blog"}
                      className="col-12 d-inline-block mb-3 px-3">
                      <p
                        className={
                          "menu_sidebar_link2 " +
                          (activeLink == "blogs" && "active")
                        }>
                        Blogs
                      </p>
                    </Link>
                    <Link
                      to={"/shops"}
                      className="col-12 d-inline-block mb-3 px-3">
                      <p
                        className={
                          "menu_sidebar_link2 " +
                          (activeLink == "shops" && "active")
                        }>
                        Shops
                      </p>
                    </Link>
                    <Link
                      to={"/coupon-partner"}
                      className="col-12 d-inline-block mb-3 px-3">
                      <p
                        className={
                          "menu_sidebar_link2 " +
                          (activeLink == "coupon-partner" && "active")
                        }>
                        Coupon Partner
                      </p>
                    </Link>
                    <Link
                      to={"/contact"}
                      className="col-12 d-inline-block mb-3 px-3">
                      <p
                        className={
                          "menu_sidebar_link2 " +
                          (activeLink == "contact" && "active")
                        }>
                        Contact
                      </p>
                    </Link>
                    <Link
                      to={"/giveaway"}
                      className="col-12 d-inline-block mb-3 px-3">
                      <p
                        className={
                          "menu_sidebar_link2 " +
                          (activeLink == "giveaway" && "active")
                        }>
                        Giveaway
                      </p>
                    </Link>
                  </div>
                }
              />
            </div>
          )} */}

          <Link
            to={"/affiliate"}
            className="col-6 col-lg-12 d-inline-block mb-3">
            <p
              className={
                "menu_sidebar_link2 " + (activeLink == "affiliate" && "active")
              }>
              {!isShort ? "Affiliate" : ""}{" "}
            </p>
          </Link>
          <Link
            to={"/how-to-sell"}
            className="col-6 col-lg-12 d-inline-block mb-3">
            <p
              className={
                "menu_sidebar_link2 " + (activeLink == "how-to-sell" && "active")
              }>
              {!isShort ? "How to sell" : ""}{" "}
            </p>
          </Link>
          <Link to={"/about"} className="col-6 col-lg-12 d-inline-block mb-3">
            <p
              className={
                "menu_sidebar_link2 " + (activeLink == "about" && "active")
              }>
              {!isShort ? "About Us" : ""}
            </p>
          </Link>
          <Link to={"/blog"} className="col-6 col-lg-12 d-inline-block mb-3">
            <p
              className={
                "menu_sidebar_link2 " + (activeLink == "blogs" && "active")
              }>
              {" "}
              {!isShort ? "Blogs" : ""}
            </p>
          </Link>
          <Link to={"/shops"} className="col-6 col-lg-12 d-inline-block mb-3">
            <p
              className={
                "menu_sidebar_link2 " + (activeLink == "shops" && "active")
              }>
              {" "}
              {!isShort ? "Shops" : ""}
            </p>
          </Link>
          <Link
            to={"/coupon-partner"}
            className="col-6 col-lg-12 d-inline-block mb-3">
            <p
              className={
                "menu_sidebar_link2 " +
                (activeLink == "coupon-partner" && "active")
              }>
              {!isShort ? "Coupon Partner" : ""}
            </p>
          </Link>
          <Link to={"/contact"} className="col-6 col-lg-12 d-inline-block mb-3">
            <p
              className={
                "menu_sidebar_link2 " + (activeLink == "contact" && "active")
              }>
              {" "}
              {!isShort ? "Contact" : ""}
            </p>
          </Link>
          <Link
            to={"/giveaway"}
            className="col-6 col-lg-12 d-inline-block mb-3">
            <p
              className={
                "menu_sidebar_link2 " + (activeLink == "giveaway" && "active")
              }>
              {" "}
              {!isShort ? "Giveaway" : ""}
            </p>
          </Link>
        </div>
      </div>

      <Link to={"/help-center"} className="d-block ">
        <p
          className={
            "menu_sidebar_help d-flex gap-3 " +
            (activeLink == "help-center" && "active")
          }>
          <img
            src={
              activeLink == "help-center"
                ? "../assets/images/icons/help_white.svg"
                : "../assets/images/icons/help.svg"
            }
            alt=""
            className=""
          />
          {!isShort ? "Help Center" : ""}
        </p>
      </Link>
    </>
  );
}
