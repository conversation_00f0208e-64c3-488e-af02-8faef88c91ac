import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getHelpCategories} from "../../api/help/getHelpCategories.js";

export const useHelpCategories = () => {
    const { data: categories, loading: categoriesLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['help'],
            queryFn: () => getHelpCategories(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { categories, categoriesLoading};
};