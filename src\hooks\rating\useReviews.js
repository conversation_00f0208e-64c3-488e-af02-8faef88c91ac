import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getReviews} from "../../api/rating/getReviews.js";

export const useReviews = (props) => {

    const { data: reviews, loading: reviewLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['reviews', props.userId],
            queryFn: () => getReviews(props),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { reviews, reviewLoading};
};