import {useQuery, useQueryClient} from "react-query";
import {showError, showSuccess, useQueryFix} from "../../vbrae-utils/index.js";
import {patchTicket} from "../../api/ticket/patchTicket.js";

export const useTicketUpdate = ({_id, state}) => {
    const queryClient = useQueryClient();
    const { refetch:ticketUpdate, loading:updateLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['ticket-update', _id],
            queryFn: () => patchTicket({_id, state}),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: false,
            onSuccess: ()=> {
                queryClient.invalidateQueries(['ticket', _id]).finally(()=> showSuccess("Ticket Closed"))
            },
        }),
        transform: (data) => data.data,
    });

    return { ticketUpdate , updateLoading};
};