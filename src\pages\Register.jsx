import {useContext} from "react";
import {ModalContext} from "../store/ModalContext";
import RegisterForm from "../components/auth/register-form.jsx";
import SocialLogin from "../components/auth/socialLogin.jsx";

export default function RegisterModal() {
  const { activeModal, OpenModal, CloseModal } = useContext(ModalContext); //Modal Global State

  if (activeModal !== "register") return null;
  return (
    <>
      <div className="modal_con d-lg-flex justify-content-center align-items-center">
        <div className="col col-lg-10 col-xl-8 register_con d-flex">
          <div
            className="col-4 d-none d-lg-block register_img"
            style={{
              backgroundImage: "url(./assets/images/register_img.png)",
            }}></div>
          <div className="col register_cont position-relative">
            {/* Close Button */}
            <span
              className="register_close position-absolute"
              role="button"
              onClick={() => CloseModal()}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"
                />
              </svg>
            </span>
            <p className="register_header">Become part of Vbrae</p>
            <SocialLogin route="register"/>
            <RegisterForm onClick={() => OpenModal("login")}/>
          </div>
        </div>
      </div>
    </>
  );
}
