import React from "react";

class ErrorBoundary extends React.Component {
    state = { hasError: false };

    static getDerivedStateFromError(error) {
        console.log(error);
        return { hasError: true };
    }

    componentDidCatch(error, errorInfo) {
        console.log(error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div className="d-flex justify-content-center align-items-center vh-100 bg-light">
                    <div className="text-center p-4 shadow rounded bg-white">
                        <h2 className="display-4 font-weight-bold mb-3">Something went wrong!</h2>
                        <p className="mb-4">
                            Oops! There was an error.<br />
                            Our team has been notified, and we are working on a resolution for you!
                        </p>
                        <button
                            type="button"
                            onClick={() => {
                                this.setState({ hasError: false });
                                window.location.href = '/';
                            }}
                            className="btn btn-primary"
                        >
                            Back to home
                        </button>
                    </div>
                </div>
            );
        } else {
            return this.props.children;
        }
    }
}

export default ErrorBoundary;
