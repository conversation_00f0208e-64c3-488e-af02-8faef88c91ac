import CustomDropdown from "../../pages/componets/utility/CustomDropdown.jsx";
import {Link} from "react-router-dom";
import {formatDateTime} from "../../vbrae-utils/lib/time.js";

export default function ReviewRow({orderNumber, index, user, content, offerName, createdAt, stars, setData, reviewId, offerId, sellerResponse}){

    const [p1, p2] = formatDateTime(createdAt)

    return (
        <tr className="acct_table_row">
            <td className="acct_table_data">#{index + 1}</td>
            <td className="acct_table_data ">{orderNumber}</td>
            <td className="acct_table_data">{user}</td>
            <td className="acct_table_data">
                          <span className="acct_table_data_icon blue">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="currentColor"
                                  d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                              />
                            </svg>
                          </span>
                {stars.toFixed(1)}
            </td>
            <td className="acct_table_data">
                {content}
            </td>
            <td className="acct_table_data blue">
                {offerName}
            </td>
            <td className="acct_table_data text-nowrap">

                {p1} <br/> / {p2}
            </td>
            <td className="acct_table_data position-relative">
                <CustomDropdown
                    trigger={() => (
                        <span className="acct_table_data_icon">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 24 24">
                                  <path
                                      fill="currentColor"
                                      d="M12 3c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 14c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-7c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2"
                                  />
                                </svg>
                              </span>
                    )}
                    content={
                        <div className="acct_table_drop_cont">
                            {sellerResponse ? null : <Link onClick={()=> setData({reviewId, offerId, isOpen: true, comment})}>
                                <p className="acct_table_drop_link">Reply</p>
                            </Link>}
                            <Link>
                                <p className="acct_table_drop_link">Delete</p>
                            </Link>
                            <Link>
                                <p className="acct_table_drop_link">
                                    To Archive
                                </p>
                            </Link>
                        </div>
                    }
                />
            </td>
        </tr>
    )
}