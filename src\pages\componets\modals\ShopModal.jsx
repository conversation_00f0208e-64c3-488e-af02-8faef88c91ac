/* eslint-disable react/prop-types */

import {useContext} from "react";
import {ModalContext} from "../../../store/ModalContext.jsx";
import ShopForm from "../shop/ShopForm.jsx";

export default function ShopModal(){
    const { activeModal, CloseModal } = useContext(ModalContext);

    if (activeModal !== "shop") return null;
    return (
        <div className="modal_con d-lg-flex justify-content-center align-items-center">
            <div className="col col-lg-6 register_con d-flex">
                <div className="col register_cont position-relative">
                    <span
                        className="register_close position-absolute"
                        role="button"
                        onClick={() => CloseModal()}>
              <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24">
                <path
                    fill="currentColor"
                    d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"
                />
              </svg>
            </span>
                    <p className="register_header">Tell Us About Your Shop</p>
                    <ShopForm />
                </div>
            </div>
        </div>
    )
}