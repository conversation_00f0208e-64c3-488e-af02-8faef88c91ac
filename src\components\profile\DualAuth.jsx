export default function DualAuth({formik}){
    return (
        <div className="col pro_tab_cont">
            <p className="acct_offer_title gray mb-3">
                Two Factor Authentication
            </p>

            <div className="col">
                <div className="col mb-4">
                    <div className="form-check form-switch d-flex gap-3 align-items-center mb-2">
                        <input
                            className="form-check-input"
                            type="checkbox"
                            role="switch"
                            id="flexSwitchCheckChecked"
                            checked={formik.values.enable2FA}
                            onChange={e => formik.setFieldValue("enable2FA", e.target.checked)}
                        />
                        <label
                            className="acct_offer_title small"
                            htmlFor="flexSwitchCheckChecked">
                            Email Two Factor Authentication
                        </label>
                    </div>
                    <p className="acct_offer_text">
                        If you want to log in to your Kinguin account or perform
                        an action that will require 2FA, we will send a
                        verification code to your email address. It will prevent
                        unauthorised log ins to your account.
                    </p>
                </div>
                {/*<div className="col mb-4">*/}
                {/*    <div className="form-check form-switch d-flex gap-3 align-items-center mb-2">*/}
                {/*        <input*/}
                {/*            className="form-check-input"*/}
                {/*            type="checkbox"*/}
                {/*            role="switch"*/}
                {/*            id="flexSwitchCheckChecked"*/}
                {/*            onChange={() => {}}*/}
                {/*        />*/}
                {/*        <label*/}
                {/*            className="acct_offer_title dark small"*/}
                {/*            htmlFor="flexSwitchCheckChecked">*/}
                {/*            Google Authenticator*/}
                {/*        </label>*/}
                {/*    </div>*/}
                {/*    <p className="acct_offer_text">*/}
                {/*        If you want to log in to your Kinguin account or perform*/}
                {/*        an action that will require 2FA, we will send a*/}
                {/*        verification code to your email address. It will prevent*/}
                {/*        unauthorised log ins to your account.*/}
                {/*    </p>*/}
                {/*</div>*/}
            </div>
        </div>
    )
}