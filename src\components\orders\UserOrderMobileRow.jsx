import {Link} from "react-router-dom";
import CustomDropdown from "../../pages/componets/utility/CustomDropdown.jsx";
import {formatDateTime} from "../../vbrae-utils/lib/time.js";

export default function UserOrderMobileRow({createdAt, status, orderNumber, total, _id}){
    const [createdA, createdB] = formatDateTime(createdAt);
    return (
        <div className="mob_table_con position-relative mb-3">
            <div className="col row gx-4 gy-3">
                <div className="col-6">
                    <label className="mob_table_head">ID</label>
                    <Link
                        to={"/account/order-details"}
                        className="mob_table_data">
                        {orderNumber}
                    </Link>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">Total</label>
                    <p className="mob_table_data">${total}</p>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">Payment</label>
                    <p className="mob_table_data bold">Payment Received</p>
                </div>

                <div className="col-6">
                    <label className="mob_table_head">STATUS</label>
                    <span
                        className={`acct_table_data_tag text-capitalize ${status === "completed" ? "green" : (status === "canceled" ? "red" : "blue")}`}>
                        {status}
                      </span>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">Date</label>
                    <p className="mob_table_data">{createdA} / {createdB}</p>
                </div>
            </div>
            <div className="mob_table_icon_con position-absolute">
                <div className="col-auto position-relative">
                    <CustomDropdown
                        trigger={() => (
                            <span className="mob_table_data_icon">
                                  <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="20"
                                      height="20"
                                      viewBox="0 0 20 20"
                                      fill="currentColor">
                                    <path
                                        d="M10 1C12.3869 1 14.6761 1.94821 16.364 3.63604C18.0518 5.32387 19 7.61305 19 10C19 12.3869 18.0518 14.6761 16.364 16.364C14.6761 18.0518 12.3869 19 10 19C7.61305 19 5.32387 18.0518 3.63604 16.364C1.94821 14.6761 1 12.3869 1 10C1 7.61305 1.94821 5.32387 3.63604 3.63604C5.32387 1.94821 7.61305 1 10 1ZM10 4.85714C9.83676 4.857 9.67529 4.8909 9.5259 4.95668C9.3765 5.02247 9.24247 5.11869 9.13237 5.2392C9.02226 5.35971 8.9385 5.50186 8.88644 5.65657C8.83438 5.81129 8.81515 5.97515 8.83 6.13771L9.29929 11.2883C9.31748 11.4614 9.39915 11.6217 9.52854 11.7383C9.65793 11.8548 9.82588 11.9192 10 11.9192C10.1741 11.9192 10.3421 11.8548 10.4715 11.7383C10.6009 11.6217 10.6825 11.4614 10.7007 11.2883L11.1687 6.13771C11.1836 5.97526 11.1644 5.8115 11.1124 5.65688C11.0604 5.50226 10.9767 5.36017 10.8668 5.23968C10.7568 5.11919 10.6229 5.02294 10.4737 4.95707C10.3245 4.89121 10.1631 4.85718 10 4.85714ZM10 15.1429C10.2728 15.1429 10.5344 15.0345 10.7273 14.8416C10.9202 14.6487 11.0286 14.3871 11.0286 14.1143C11.0286 13.8415 10.9202 13.5799 10.7273 13.387C10.5344 13.1941 10.2728 13.0857 10 13.0857C9.72721 13.0857 9.46558 13.1941 9.27269 13.387C9.0798 13.5799 8.97143 13.8415 8.97143 14.1143C8.97143 14.3871 9.0798 14.6487 9.27269 14.8416C9.46558 15.0345 9.72721 15.1429 10 15.1429Z"/>
                                  </svg>
                                </span>
                        )}
                        content={
                            <div className="acct_table_drop_cont">
                                <Link>
                                    <p className="acct_table_drop_link">
                                        Order again
                                    </p>
                                </Link>
                                <Link>
                                    <p className="acct_table_drop_link">
                                        Download key
                                    </p>
                                </Link>
                            </div>
                        }
                    />
                </div>
            </div>
        </div>
    )
}