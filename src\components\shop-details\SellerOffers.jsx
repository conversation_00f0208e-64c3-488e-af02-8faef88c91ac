import ShopFilters from "./ShopFilters.jsx";
import Pagination from "../../pages/componets/utility/Pagination.jsx";
import {useEffect, useState} from "react";
import {useSellerOffers} from "../../hooks/offers/useSellerOffers.js";
import {generateQuery} from "../../vbrae-utils/lib/misc.js";
import Spinner from "../common/Spinner.jsx";
import GameItem from "../../pages/componets/GameItem.jsx";
import EmptyGameItem from "../../pages/componets/EmptyGameItem.jsx";
import {getUserId} from "../../vbrae-utils/index.js";

export default function SellerOffers({sellerId, setShopCounts}){

    const [page, setPage] = useState(1);
    const [selectedRegion, setSelectedRegion] = useState("");
    const [selectedLanguage, setSelectedLanguage] = useState("");
    const [selectedGenre, setSelectedGenre] = useState("");
    const [prices, setPrices] = useState([]);
    const [selectedBoxes, setSelectedBoxes] = useState({});
    const [isFilterOpen, setIsFilterOpen] = useState(false);
    const [filledCards, setFilledCards] = useState([]);

    const userId = getUserId();

    const {offers, offersLoading} = useSellerOffers({query: generateQuery({
            limit: 12,
            staticString: userId && `userId=${getUserId()}`,
            selectedBoxes,
            prices,
            selectedGenre,
            selectedRegion,
            page,
            selectedLanguage,
            userId: sellerId
        })})

    useEffect(() => {

        if(!offers) return;

        fillGrid();
        window.addEventListener("resize", fillGrid); // Refill on resize
        setShopCounts(prev=> ({...prev, offers: offers.data.length}))
        return () => window.removeEventListener("resize", fillGrid);
    }, [offers]);

    const fillGrid = () => {
        const container = document.querySelector(".game_list_con");
        const containerWidth = container.clientWidth; // Get container width
        const itemsPerRow = Math.floor(containerWidth / 250); // Calculate items that can fit

        const totalItems = Math.ceil(offers.data.length / itemsPerRow) * itemsPerRow; // Calculate total items needed
        const placeholders = totalItems - offers.data.length; // Calculate number of placeholders needed
        const filled = [...offers.data, ...Array(placeholders).fill(null)]; // Create filled array

        setFilledCards(filled);
    };

    const resetFilters = () => {
        setSelectedRegion("");
        setSelectedLanguage("");
        setSelectedGenre("");
        setPrices([]);
        setSelectedBoxes({})
    }

    function OpenFilter() {
        setIsFilterOpen(true);
    }
    function CloseFilter() {
        setIsFilterOpen(false);
    }

    return (
        <div className="col d-flex gap-4 position-relative">
            <ShopFilters
                isOpen={isFilterOpen}
                onClose={CloseFilter}
                selectedRegion={selectedRegion}
                setSelectedRegion={setSelectedRegion}
                selectedLanguage={selectedLanguage}
                prices={prices}
                selectedGenre={selectedGenre}
                setSelectedLanguage={setSelectedLanguage}
                setSelectedGenre={setSelectedGenre}
                setPrices={setPrices}
                setSelectedBoxes={setSelectedBoxes}
                selectedBoxes={selectedBoxes}
            />

            <div className="w-100">
                <div
                    className="col d-xl-flex flex-row-reverse align-items-start align-items-xl-center gap-3 gap-xl-4 mb-4">
                    <div className="col col-xl-3 d-flex gap-2 align-items-center mb-3 mb-xl-0">
                        <p
                            onClick={OpenFilter}
                            className="col d-flex d-lg-none cate_filter_tigger align-items-center gap-2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                                <g fill="none" fillRule="evenodd">
                                    <path
                                        d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"/>
                                    <path
                                        fill="currentColor"
                                        d="M3 4.5A1.5 1.5 0 0 1 4.5 3h15A1.5 1.5 0 0 1 21 4.5v2.086A2 2 0 0 1 20.414 8L15 13.414v7.424a1.1 1.1 0 0 1-1.592.984l-3.717-1.858A1.25 1.25 0 0 1 9 18.846v-5.432L3.586 8A2 2 0 0 1 3 6.586z"
                                    />
                                </g>
                            </svg>
                            Filter by
                            <span className="d-flex justify-content-center align-items-center ms-auto">
                                6
                              </span>
                        </p>
                        <select className="col col-lg-6 col-xl cate_filter_sort_sel">
                            <option value="">Most recent</option>
                        </select>
                    </div>

                    <div className="col d-flex gap-2 overflow-auto hide_scroll">
                        <span className="cate_filter_tag" onClick={resetFilters}>
                              Clear all
                            <svg
                                className="ms-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                              <path
                                  fill="currentColor"
                                  d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                              />
                            </svg>
                          </span>

                        {Object.entries(selectedBoxes).map(([key, value], index) => {
                            if (key === "delivery") {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState.delivery;
                                              return newState;
                                          })}
                                          key={index}>
                                    {key} {value.join('/')}
                                        <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em"
                                             height="1em" viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                );
                            } else if (key === 'stock[gt]') {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState[`stock[gt]`];
                                              return newState;
                                          })}
                                          key={index}>
                                    In stock
                                <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                     viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                );
                            } else if (key === 'instantDelivery' && value[0]) {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState.instantDelivery;
                                              return newState;
                                          })}
                                          key={index}>
                                    Pre Order
                                <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em"
                                     viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                )
                            } else if (key === 'region') {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState.region;
                                              return newState;
                                          })}
                                          key={index}>
                                    {selectedBoxes?.region[0]}
                                        <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em"
                                             height="1em"
                                             viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                )
                            } else if (key === 'category') {
                                return (
                                    <span className="cate_filter_tag text-capitalize"
                                          onClick={() => setSelectedBoxes(prev => {
                                              const newState = {...prev};
                                              delete newState.category;
                                              return newState;
                                          })}
                                          key={index}>
                                    {selectedBoxes?.category[0]}
                                        <svg className="ms-1" xmlns="http://www.w3.org/2000/svg" width="1em"
                                             height="1em"
                                             viewBox="0 0 32 32">
                                      <path fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"/>
                                    </svg>
                                  </span>
                                )
                            }
                            return null;
                        })}

                        {prices[0] &&
                            <span className="cate_filter_tag" onClick={() => setPrices([undefined, prices[1]])}>
                      Min: ${prices[0]}
                                <svg
                                    className="ms-1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                        {prices[1] &&
                            <span className="cate_filter_tag" onClick={() => setPrices([prices[0], undefined])}>
                      Max: ${prices[1]}
                                <svg
                                    className="ms-1"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                        {selectedRegion && <span className="cate_filter_tag" onClick={() => setSelectedRegion("")}>
                      {selectedRegion}
                            <svg
                                className="ms-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                        {selectedLanguage && <span className="cate_filter_tag" onClick={() => setSelectedLanguage("")}>
                      {selectedLanguage}
                            <svg
                                className="ms-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                        {selectedGenre && <span className="cate_filter_tag" onClick={() => setSelectedGenre("")}>
                      {selectedGenre}
                            <svg
                                className="ms-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                    </div>

                    <div className="col d-none d-flex gap-2 overflow-auto">
                            <span className="cate_filter_head text-nowrap my-auto">
                              34 ITEMS
                            </span>

                        <span className="cate_filter_tag">
                              Clear all
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                        <span className="cate_filter_tag">
                              Switzerland
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                        <span className="cate_filter_tag">
                              Instant
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                        <span className="cate_filter_tag active">
                              24H
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                        <span className="cate_filter_tag">
                              Max: $1000
                              <svg
                                  className="ms-1"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 32 32">
                                <path
                                    fill="currentColor"
                                    d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>
                    </div>
                </div>

                {offersLoading || !offers ? <Spinner/> : <>
                    <div className="col game_list_con row justify-content-start g-3 mb-4">
                        {filledCards.map((card, index) => (
                            <>
                                {card !== null ? (
                                    <GameItem key={index} {...card} />
                                ) : (
                                    <EmptyGameItem />
                                )}
                            </>
                        ))}
                    </div>
                    <div className="col d-flex justify-content-center">
                        {offers.pagination.pages && <Pagination
                            totalPages={offers.pagination.pages}
                            currentPage={page}
                            pageClick={(page) => setPage(page)}
                            nextPage={() => setPage(page + 1)}
                            prevPage={() => setPage(page - 1)}/>}
                    </div>
                </>}

            </div>
        </div>
    )
}