import {postRequest} from "../../vbrae-utils/index.js";

const urls = {
    hostUrls: [window.location.origin],
    completeUrl: `${window.location.origin}/thank-you?status=completed`,
    cancelUrl: `${window.location.origin}?status=cancelled`,
    callbackUrl: 'https://clownfish-app-n9lv5.ondigitalocean.app/payment/callback',
    logoUrl: 'https://example.com/logo.png',
    termsOfServiceUrl: 'https://example.com/terms.pdf',
}

export async function createPayment(props){
    const r = await postRequest({
        url: `payment/create-swedbank`,
        data: {...props, urls},
        useAuth: true,
    });
    return r.response;
}