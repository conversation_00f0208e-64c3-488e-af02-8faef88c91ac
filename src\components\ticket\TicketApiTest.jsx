import { useState } from 'react';
import { useTickets } from '../../hooks/ticket/useTickets.js';
import { useTicketDetails } from '../../hooks/ticket/useTicketDetails.js';
import { generateTicketQuery } from '../../vbrae-utils/lib/misc.js';

export default function TicketApiTest() {
    const [testTicketId, setTestTicketId] = useState('');
    const [page, setPage] = useState(1);
    const [status, setStatus] = useState('open');
    const [search, setSearch] = useState('');

    // Test getting tickets list
    const { tickets, ticketsLoading } = useTickets({
        query: generateTicketQuery({
            page,
            limit: 5,
            status,
            search
        })
    });

    // Test getting single ticket details
    const { ticketDetails, ticketDetailsLoading } = useTicketDetails({
        _id: testTicketId
    });

    return (
        <div style={{ padding: '20px', border: '1px solid #ccc', margin: '20px' }}>
            <h3>Ticket API Integration Test</h3>
            
            {/* Test Tickets List */}
            <div style={{ marginBottom: '20px' }}>
                <h4>Test Tickets List API</h4>
                <div style={{ marginBottom: '10px' }}>
                    <label>Page: </label>
                    <input 
                        type="number" 
                        value={page} 
                        onChange={(e) => setPage(parseInt(e.target.value))}
                        style={{ marginRight: '10px', width: '60px' }}
                    />
                    
                    <label>Status: </label>
                    <select 
                        value={status} 
                        onChange={(e) => setStatus(e.target.value)}
                        style={{ marginRight: '10px' }}
                    >
                        <option value="open">Open</option>
                        <option value="closed">Closed</option>
                        <option value="pending">Pending</option>
                        <option value="">All</option>
                    </select>
                    
                    <label>Search: </label>
                    <input 
                        type="text" 
                        value={search} 
                        onChange={(e) => setSearch(e.target.value)}
                        placeholder="Search tickets..."
                        style={{ width: '150px' }}
                    />
                </div>
                
                <div>
                    <strong>API URL:</strong> /tickets/my-tickets?{generateTicketQuery({ page, limit: 5, status, search })}
                </div>
                
                {ticketsLoading ? (
                    <p>Loading tickets...</p>
                ) : tickets ? (
                    <div>
                        <p><strong>Response:</strong></p>
                        <pre style={{ background: '#f5f5f5', padding: '10px', fontSize: '12px' }}>
                            {JSON.stringify(tickets, null, 2)}
                        </pre>
                    </div>
                ) : (
                    <p>No tickets data</p>
                )}
            </div>

            {/* Test Single Ticket */}
            <div>
                <h4>Test Single Ticket API</h4>
                <div style={{ marginBottom: '10px' }}>
                    <label>Ticket ID: </label>
                    <input 
                        type="text" 
                        value={testTicketId} 
                        onChange={(e) => setTestTicketId(e.target.value)}
                        placeholder="Enter ticket ID..."
                        style={{ width: '200px' }}
                    />
                </div>
                
                {testTicketId && (
                    <div>
                        <strong>API URL:</strong> /tickets/{testTicketId}
                    </div>
                )}
                
                {testTicketId && (
                    ticketDetailsLoading ? (
                        <p>Loading ticket details...</p>
                    ) : ticketDetails ? (
                        <div>
                            <p><strong>Response:</strong></p>
                            <pre style={{ background: '#f5f5f5', padding: '10px', fontSize: '12px' }}>
                                {JSON.stringify(ticketDetails, null, 2)}
                            </pre>
                        </div>
                    ) : (
                        <p>No ticket details data</p>
                    )
                )}
            </div>
        </div>
    );
}
