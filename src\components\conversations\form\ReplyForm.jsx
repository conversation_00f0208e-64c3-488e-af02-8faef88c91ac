import useMessageForm from "../../../hooks/conversation/useMessageForm.js";
import {useRef} from "react";

export default function ReplyForm(){

    const ref = useRef();

    const {formik} = useMessageForm()

    const removeFile = (index) => {
        const newFiles = [...formik.values.attachments];
        newFiles.splice(index, 1);
        formik.setFieldValue("attachments", newFiles);
    };

    return (
        <form className="msg_box_con" onSubmit={formik.handleSubmit}>
            <div className="col mb-4">
                <div className="col acct_box">
                        <textarea
                            name=""
                            className="input_box"
                            placeholder="Write your message"
                            {...formik.getFieldProps("content")}
                            rows={3}>
                        </textarea>
                </div>
                {formik.errors.content && formik.touched.content && (
                    <small className="main_footer_copy text-danger">{formik.errors.content}</small>
                )}
            </div>

            <div className="col d-flex flex-wrap gap-3 justify-content-between  align-items-start">
                <div className="col-12 col-xxl d-flex flex-wrap gap-2">
                    <div className="col-12 col-md-6 acct_offer_file_con">
                        <div className="acct_offer_file p-2">
                            <p className="acct_offer_text text-center" onClick={()=>ref.current?.click()}>
                                <span className="bold">Attachments </span>
                                (Max 3 files)
                            </p>

                            <input
                                ref={ref}
                                type="file"
                                multiple
                                onChange={(files) => {
                                    const selectedFiles = Array.from(files.target.files);
                                    const combined = [...formik.values.attachments, ...selectedFiles];
                                    const lastThree = combined.slice(-3);
                                    formik.setFieldValue("attachments", lastThree);
                                }}
                                accept="*"
                                className="mb-2 d-none"
                            />

                        </div>
                    </div>
                    <div className="col align-items-start">
                            {formik.values.attachments.length > 0 && <>
                            <div className="d-flex gap-2 ">
                                <p className="acct_offer_text mb-1">
                                    <span className="bold">Attachments </span>
                                    (Max 3 files)
                                </p>
                            </div>
                                {formik.values.attachments.map((file, index) => (
                                    <span className="cate_filter_tag" key={index} onClick={()=> removeFile(index)}>
                                      File #{index + 1}
                                        <svg
                                            className="ms-1"
                                            xmlns="http://www.w3.org/2000/svg"
                                            width="1em"
                                            height="1em"
                                            viewBox="0 0 32 32">
                                        <path
                                            fill="currentColor"
                                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                        />
                                      </svg>
                                    </span>
                                ))}
                            </>}
                    </div>
                </div>
                <button
                    disabled={formik.isSubmitting || !formik.isValid}
                    type="submit"
                    className="col col-md-auto acct_offer_btn1">
                    {formik.isSubmitting ? "Sending..." : "Send"}
                </button>
            </div>
        </form>
    )
}