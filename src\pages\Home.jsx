import ArticleItem from "./componets/articles/ArticleItem";
import MenuSideBar from "./componets/MenuSideBar";
import MainHeader from "./componets/MainHeader";
import RightSideBar from "./componets/RightSideBar";
import Subscribe from "./componets/utility/Subscribe";
import MainFooter from "./componets/MainFooter";
// import Swiper core and required modules
import {Pagination} from "swiper/modules";
import {Swiper, SwiperSlide} from "swiper/react";
// Import Swiper styles
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/pagination";
import "swiper/css/scrollbar";
import TopOffers from "./componets/home/<USER>";
import NewArrivals from "./componets/home/<USER>";
import Genres from "./componets/home/<USER>";
import Software from "./componets/home/<USER>";
import {useHomeConfigs} from "../hooks/home-configs/useHomeConfigs.js";
import {useBanners} from "../hooks/banners/useBanners.js";
import Spinner from "../components/common/Spinner.jsx";
import {useBlogs} from "../hooks/blogs/useBlogs.js";
import BannerOne from "../components/banners/BannerOne.jsx";
import BannerTwo from "../components/banners/BannerTwo.jsx";
import BannerThree from "../components/banners/BannerThree.jsx";
import BannerFour from "../components/banners/BannerFour.jsx";

export default function Home() {

    const {blogs} = useBlogs({query: ''});
    const {settings} = useHomeConfigs();
    const {banners, bannerLoading} = useBanners();

  return (
      <>
        <div className="d-flex main_house_con">
        <MenuSideBar activeLink={"home"} />

        <div
          className="col housing_con d-flex flex-column"
          style={
            {
              // backgroundImage: " url('./assets/images/bg.png')",
            }
          }>
          <MainHeader activeLink={"home"} />

          <div className="housing d-flex gap-1 position-relative">
            <div id="scrollable-section" className="col main_section">
              {/* Banner Section */}
              <div className="banner_con d-lg-flex gap-3 mb-5">
                {(!banners || bannerLoading) ? <Spinner /> : <>
                  <Swiper
                      modules={[Pagination]}
                      pagination={{
                        // el: "",
                        clickable: true,
                        renderBullet: function (index, className) {
                          // Customize the pagination bullets with your own HTML structure and classes
                          return `<span class="${className} banner_custom_bullet mb-3 mb-lg-0"></span>`;
                        }
                      }}
                      onSwiper={(swiper) => {
                        swiper.wrapperEl.classList.add("game_slider_con");
                      }}
                      slidesPerView={1}
                      autoplay={true}>
                    {banners.slice(0,2).map((banner, index)=> (
                        <SwiperSlide key={index}>
                            <BannerOne {...banner}/>
                        </SwiperSlide>
                    ))}
                  </Swiper>
                    <div className="col col-lg-4 d-flex flex-column gap-3">
                        <BannerTwo {...banners[2]} />
                        <BannerThree {...banners[3]} />
                    </div>
                </>}
              </div>

                {/* Top Offers Section */}
                {settings ? settings.topOffers.enabled && <TopOffers limit={settings.topOffers.limit}/> :
                    <Spinner/>}

                {/* Main Offer Section */}
                <div className="main_ofer_con d-lg-flex gap-3 mb-5 position-relative">
                    {banners?.slice(4, 6).map((banner, index) => (
                        <BannerFour {...banner} key={index} />
                    ))}
                </div>

                {/* Subscribe Section */}
                <Subscribe customClass={"mb-5"}/>

                {/* Software Category */}
                {settings ? settings.softwares.enabled && <Software limit={settings.softwares.limit}/> :
                    <Spinner/>}

                {/* Genres Section */}
                <Genres/>

                {/* New Arrivals Section */}
                {settings ? settings.newArrivals.enabled && <NewArrivals limit={settings.newArrivals.limit}/> :
                    <Spinner/>}

                {/* Articles Section */}
                <div className="main_cate_con ">
                    <div className="d-flex justify-content-between align-items-center overflow-auto mb-4">
                        <h3 className="main_title">Latest Articles</h3>
                    </div>
                    <Swiper
                        modules={[Pagination]}
                        spaceBetween={10}
                        // slidesPerView={2}
                        pagination={{
                            el: ".custom_pagination",
                            clickable: true,
                            renderBullet: function (index, className) {
                                // Customize the pagination bullets with your own HTML structure and classes
                                return `<span class="${className} custom_bullet"></span>`;
                            },
                        }}
                        onSwiper={(swiper) => {
                            swiper.wrapperEl.classList.add("game_slider_con");
                        }}
                        breakpoints={{
                            640: {
                                slidesPerView: 2,
                                spaceBetween: 15,
                            },
                            768: {
                                slidesPerView: 3,
                                spaceBetween: 20,
                            },
                            1230: {
                                slidesPerView: 4,
                                spaceBetween: 20,
                            },
                        }}>
                        {/* Generate the slides dynamically */}
                        {blogs && blogs.data.map((item, index) => (
                            <SwiperSlide key={index}>
                                <ArticleItem customClass={"col-12"} {...item} />
                            </SwiperSlide>
                        ))}
                        {/* Custom Pagination */}
                        <div className="custom_pagination d-flex justify-content-center my-4"></div>
                    </Swiper>
                </div>

                <div className="col d-lg-none">
                    <MainFooter/>
                </div>
            </div>

              <RightSideBar/>
          </div>
        </div>
        </div>
      </>
  );
}
