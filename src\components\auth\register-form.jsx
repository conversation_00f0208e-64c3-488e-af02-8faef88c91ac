import {<PERSON>} from "react-router-dom";
import useRegisterForm from "../../hooks/auth/useRegisterForm.js";
import {passwordKeys, passwordRequirements} from "../../constant/auth.js";

export default function RegisterForm({...otherProps}){

    const {formik} = useRegisterForm();

    return (
        <form onSubmit={formik.handleSubmit}>
            <div className="col">

                <div className="col mb-3">
                    <label
                        htmlFor="name"
                        className="auth_label text-uppercase mb-2">
                        Full Name
                    </label>
                    <input
                        type="text"
                        name="name"
                        className="auth_input"
                        {...formik.getFieldProps('name')}
                    />
                    {formik.touched.name && <small className="main_footer_copy text-danger">{formik.errors.name}</small>}
                </div>

                <div className="col mb-3">
                    <label
                        htmlFor="email"
                        className="auth_label text-uppercase mb-2">
                        Email
                    </label>
                    <input
                        type="email"
                        name="email"
                        className="auth_input"
                        {...formik.getFieldProps('email')}
                    />
                    {formik.touched.email && <small className="main_footer_copy text-danger">{formik.errors.email}</small>}
                </div>
                <div className="col mb-3">
                    <label
                        htmlFor="password"
                        className="auth_label text-uppercase mb-2">
                        Password
                    </label>
                    <input
                        type="password"
                        name="password"
                        className="auth_input"
                        {...formik.getFieldProps('password')}
                    />
                    {formik.touched.password && <small className="main_footer_copy text-danger">{formik.errors.password}</small>}
                </div>

                <div className="col mb-2">
                    <p className="auth_head text-uppercase">
                        Your password must have
                    </p>
                    <div className="col row row-cols-md-2">
                        {passwordKeys.map((item, index) => (
                            <div className="col d-flex align-items-center gap-2 mb-2" key={index}>
                                <input
                                    className="col-auto auth_check"
                                    type="checkbox"
                                    value=""
                                    onChange={()=>{}}
                                    checked={passwordRequirements(formik.values.password)[item.key]}
                                    id={item.id}
                                />
                                <label className="auth_check_text" htmlFor={item.id}>
                                    {item.label}
                                </label>
                            </div>
                        ))}
                    </div>
                </div>

                <div className="col mb-3">
                    <label
                        htmlFor="password"
                        className="auth_label text-uppercase mb-2">
                        Repeat Password
                    </label>
                    <input
                        type="password"
                        name="confirmPassword"
                        className="auth_input"
                        {...formik.getFieldProps('confirmPassword')}
                    />
                    {formik.touched.confirmPassword &&
                        <small className="main_footer_copy text-danger">{formik.errors.confirmPassword}</small>}
                </div>
            </div>

            <div className="col d-flex align-items-center gap-2 mb-5">
                <input
                    className="auth_check"
                    type="checkbox"
                    value=""
                    id="acceptTOS"
                    {...formik.getFieldProps('acceptTOS')}
                />
                <label className="register_text" htmlFor="acceptTOS">
                    I have read and agree to the <Link>Terms & Conditions</Link>
                </label>
            </div>

            <div className="col d-md-flex align-items-center gap-4">
                <button disabled={!formik.isValid || formik.isSubmitting} className="col-12 col-md-auto auth_btn">
                    {formik.isSubmitting ? "Processing..." : "Register"}
                </button>
                <p className="register_text text-white mt-3 mt-md-0 mb-0">
                    Have an account?
                    <Link className="text-decoration-none" {...otherProps}>Login</Link>
                </p>
            </div>
        </form>
    )
}