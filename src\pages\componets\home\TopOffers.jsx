import {Link} from "react-router-dom";
import {Swiper, SwiperSlide} from "swiper/react";
import {Pagination} from "swiper/modules";
import GameItem from "../GameItem.jsx";
import {useTopOffers} from "../../../hooks/offers/useTopOffers.js";
import {generateQuery} from "../../../vbrae-utils/lib/misc.js";
import {useRef, useState} from "react";
import {getUserId} from "../../../vbrae-utils/index.js";
import Spinner from "../../../components/common/Spinner.jsx";
import {categoryOptions} from "../MainHeader.jsx";

const categoryFilters = categoryOptions.slice(1);

const priceFilters = [
    10, 20, 100
]

export default function TopOffers({limit}){

    const itemRefs = useRef({});

    const [activeCategory, setActiveCategory] = useState(categoryFilters[0]._id);
    const [activePrice, setActivePrice] = useState(priceFilters[0]);
    const userId = getUserId();

    const {topOffers, topOfferLoading} = useTopOffers({query: generateQuery({
            limit,
            page: 1,
            staticString: `customerPays[lte]=${activePrice}&stock[gt]=0${userId ? `&userId=${userId}` : ''}`,
            selectedCategory: activeCategory.toLowerCase(),
        })});

    return (
        <div className="main_cate_con mb-5">
            <div className="d-lg-flex justify-content-between align-items-center mb-4">
                <h3 className="main_title d-inline-block mb-3 mb-lg-0">
                    Top Offers
                </h3>
                <div className="d-flex header-nav hide_scroll">
                    {categoryFilters.map(category => (
                        <Link to="#" onClick={() => {
                            setActiveCategory(category._id);
                            itemRefs.current[category._id]?.scrollIntoView({
                                behavior: 'smooth',
                                inline: 'center',
                                block: 'nearest'
                            });

                        }} className="me-2" key={category._id}>
                            <p
                                ref={el => itemRefs.current[category._id] = el}
                                className={`main_cate_tab_link ${category._id === activeCategory ? "active" : ""}`}>
                                {category.title}
                            </p>
                        </Link>
                    ))}
                </div>
                <div className="d-none d-lg-flex">
                    {priceFilters.map(item=> (
                        <Link to="#" onClick={()=> setActivePrice(item)} className="me-2" key={item}>
                            <p
                                className={`main_cate_tab_link px-0 ${item === activePrice ? "active" : ""}`}>
                                &lt;{item}$
                            </p>
                        </Link>
                    ))}
                </div>
            </div>

            {(!topOffers || topOfferLoading) ? <Spinner className="my-5" /> : <Swiper
                modules={[Pagination]}
                spaceBetween={10}
                slidesPerView={"auto"}
                pagination={{
                    el: ".custom_pagination",
                    clickable: true,
                    renderBullet: function (index, className) {
                        return `<span class="${className} custom_bullet"></span>`;
                    },
                }}
                onSwiper={(swiper) => {
                    swiper.wrapperEl.classList.add("game_slider_con");
                }}
            >
                {topOffers.map((offer, index) => (
                    <SwiperSlide key={index} style={{ width: "auto" }}>
                        <GameItem {...offer} />
                    </SwiperSlide>
                ))}

                <div className="custom_pagination d-flex justify-content-center mt-4"></div>
            </Swiper>
            }
        </div>
    )
}