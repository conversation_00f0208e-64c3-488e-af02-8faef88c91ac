export const generateQuery = ({
                                  searchState,
                                  selectedCategory,
                                  selectedRegion,
                                  selectedLanguage,
                                  prices,
                                  page,
                                  limit,
                                  userId,
                                  active,
                                  selectedStock,
                                  selectedGenre,
                                  staticString,
                                  selectedBoxes,
                                  createdAt,
                                  status,
                                  payment,
                                  orderNumber
                              }) => {

    let queryString = '';

    if (searchState) {
        queryString += `search=${searchState}`;
    }

    if (status) {
        if (queryString) queryString += '&';
        queryString += `status=${status}`;
    }

    if (payment) {
        if (queryString) queryString += '&';
        queryString += `payment=${payment}`;
    }

    if (orderNumber) {
        if (queryString) queryString += '&';
        queryString += `orderId=${orderNumber}`;
    }

    if (createdAt) {
        if (queryString) queryString += '&';
        queryString += `createdAt=${createdAt}`;
    }

    if (selectedCategory) {
        if (queryString) queryString += '&';
        queryString += `category=${selectedCategory}`;
    }

    if (selectedStock) {
        if (queryString) queryString += '&';
        queryString += selectedStock;
    }

    if (selectedGenre) {
        if (queryString) queryString += '&';
        queryString += `genre=${selectedGenre}`;
    }

    if (selectedRegion) {
        if (queryString) queryString += '&';
        queryString += `region=${selectedRegion}`;
    }

    if (selectedLanguage) {
        if (queryString) queryString += '&';
        queryString += `language=${selectedLanguage}`;
    }
    if (prices && prices.length > 0) {
        const [min, max] = prices;
        if (min !== undefined) {
            if (queryString) queryString += '&';
            queryString += `price[gt]=${min}`;
        }
        if (max !== undefined) {
            if (queryString) queryString += '&';
            queryString += `price[lte]=${max}`;
        }
    }

    if (userId) {
        if (queryString) queryString += '&';
        queryString += `seller=${userId}`;
    }

    if (typeof active === 'boolean') {
        if (queryString) queryString += '&';
        queryString += `active=${active}`;
    }

    if(page){
        if (queryString) queryString += '&';
        queryString += `page=${page}`;
    }

    if(limit){
        if (queryString) queryString += '&';
        queryString += `limit=${limit}`;
    }
    if(staticString){
        if (queryString) queryString += '&';
        queryString += staticString;
    }

    if (selectedBoxes && Object.keys(selectedBoxes).length > 0) {
        Object.entries(selectedBoxes).forEach(([key, value]) => {
            value.forEach((val) => {
                if (queryString) queryString += '&';
                queryString += `${key}=${val}`;
            });
        });
    }

    return queryString;
};

export const connectListing = (str) => {
    return str
        .split('\n')
        .filter((line) => line.length > 0);
};

export const truncateText = (text, maxLength) => {
    return text.length > maxLength ? text.slice(0, maxLength) + "..." : text;
};

export function calculateSavingsPercentage(actualPrice, salePrice) {
    const savings = actualPrice - salePrice;
    const percentage = (savings / actualPrice) * 100;
    return -percentage.toFixed(2)
}

export const generateSaleQuery = ({
                                      productName,
                                      orderIncrementId,
                                      mostRecent,
                                      releaseDate,
                                      status,
                                      createdDateFrom,
                                      createdDateTo,
                                      reservationId,
                                      releaseDateTo,
                                      releaseDateFrom
                                  }) => {
    let queryString = '';

    if (productName) {
        queryString += `productName=${productName}`;
    }
    if (orderIncrementId) {
        if (queryString) queryString += '&';
        queryString += `orderIncrementId=${orderIncrementId}`;
    }
    if (releaseDateTo) {
        if (queryString) queryString += '&';
        queryString += `releaseDateTo=${releaseDateTo}`;
    }
    if (releaseDateFrom) {
        if (queryString) queryString += '&';
        queryString += `releaseDateFrom=${releaseDateFrom}`;
    }
    if (reservationId) {
        if (queryString) queryString += '&';
        queryString += `reservationId=${reservationId}`;
    }
    if (mostRecent) {
        if (queryString) queryString += '&';
        queryString += `mostRecent=${mostRecent}`;
    }
    if (releaseDate) {
        if (queryString) queryString += '&';
        queryString += `releaseDate=${releaseDate}`;
    }
    if (status) {
        if (queryString) queryString += '&';
        queryString += `status=${status}`;
    }
    if (createdDateFrom) {
        if (queryString) queryString += '&';
        queryString += `createdDateFrom=${createdDateFrom}`;
    }
    if (createdDateTo) {
        if (queryString) queryString += '&';
        queryString += `createdDateTo=${createdDateTo}`;
    }

    return queryString;
};

// Generate query string specifically for tickets API
export const generateTicketQuery = ({
    page = 1,
    limit = 10,
    status = 'open',
    search,
    sortBy,
    sortOrder = 'desc'
}) => {
    let queryString = `page=${page}&limit=${limit}`;

    if (status) {
        queryString += `&status=${status}`;
    }

    if (search) {
        queryString += `&search=${encodeURIComponent(search)}`;
    }

    if (sortBy) {
        queryString += `&sortBy=${sortBy}&sortOrder=${sortOrder}`;
    }

    return queryString;
};
