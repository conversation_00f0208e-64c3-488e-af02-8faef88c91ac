import * as Yup from 'yup';
import {useMutation} from 'react-query';
import {useFormik} from 'formik';
import {getUserId, showError, showSuccess} from "../../../vbrae-utils/index.js";
import {postReport} from "../../../api/orders/reports/postReport.js";
import {uploadUrl} from "../../../api/uploadUrl.js";
import {useParams} from "react-router-dom";

// Validation schema
const reportSchema = Yup.object().shape({
    problemType: Yup.string()
        .oneOf(['Revoked', 'Already Redeemed', 'Other'])
        .required('Problem type is required'),

    offerId: Yup.string()
        .required('Offer ID is required'),

    key: Yup.string()
        .trim()
        .required('Key is required'),

    preferredSolution: Yup.string()
        .oneOf(['Refund in store Balance', 'Provide new Key'])
        .nullable(),

    comment: Yup.string()
        .trim()
        .max(1000, 'Maximum 1000 characters'),

    attachments: Yup.array()
        .of(Yup.string())
        .max(3, 'Maximum 3 attachments allowed'),

    priority: Yup.string()
        .oneOf(['Low', 'Medium', 'High'])
        .nullable(),

    status: Yup.string()
        .oneOf(['Pending', 'In Progress', 'Resolved', 'Rejected'])
        .nullable(),
});

export default function useReportForm({onClose}) {

    const userId = getUserId();
    const orderId = useParams().id;

    const initialValues = {
        problemType: 'Revoked',
        offerId: '',
        userId: userId,
        key: '',
        preferredSolution: '',
        comment: '',
        attachments: [],
        status: 'Pending',
        priority: 'Medium',
    };

    const uploadImage = async (image) => {
        if (image instanceof File) {
            const fileType = image.name.split('.').pop()?.toLowerCase() || 'unknown';
            const resignedResponse = await uploadUrl({ name: image.name, fileType });
            const { url: resignedUrl, path: filePath } = resignedResponse;

            await fetch(resignedUrl, {
                method: 'PUT',
                headers: { 'Content-Type': image.type, 'x-amz-acl': 'public-read' },
                body: image,
            });

            return filePath;
        }
        return image;
    };

    const { mutateAsync } = useMutation(postReport, {
        onError: (error) => showError(error),
    });

    const formik = useFormik({
        initialValues,
        validationSchema: reportSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const imageUrls = await Promise.all(values.attachments.map(async (attachment) => await uploadImage(attachment)))

            const response = await mutateAsync({...values, userId, orderId, attachments: imageUrls});
            setSubmitting(false);
            if (response) {
                resetForm();
                onClose();
                showSuccess("Report submitted successfully. We will contact you soon.");
            }
        },
    });

    return { formik };
}
