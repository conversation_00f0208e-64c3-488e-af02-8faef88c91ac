import {useQuery, useQueryClient} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {deleteCart} from "../../api/cart/deleteCart.js";

export const useDeleteCart = (props) => {

    const queryClient = useQueryClient();

    const { loading: deleteLoading, refetch: deleteRefetch } = useQueryFix({
        query: useQuery({
            queryKey: ['delete-cart', props.offerId],
            queryFn: () => deleteCart(props),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: false,
            onSuccess: ()=> queryClient.invalidateQueries(['get-cart']).finally(),
        }),
        transform: (data) => data,
    });

    return { deleteLoading, deleteRefetch };
};