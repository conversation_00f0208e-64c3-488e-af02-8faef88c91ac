import {useState} from "react";

const FileUpload = ({ onFilesSelected }) => {
    const [files, setFiles] = useState([]);

    const handleFileChange = (event) => {
        const selectedFiles = Array.from(event.target.files);
        setFiles(selectedFiles);
        if (onFilesSelected) onFilesSelected(selectedFiles);
    };

    const handleDrop = (event) => {
        event.preventDefault();
        const droppedFiles = Array.from(event.dataTransfer.files);
        setFiles(droppedFiles);
        if (onFilesSelected) onFilesSelected(droppedFiles);
    };

    return (
        <div className="col mb-4">
            <p className="vb_head mb-2">
                Attachments <span>(optional)</span>
            </p>
            <div className="col col-md-6 vb_contact_file_con">
                <div
                    className="vb_contact_file"
                    onDragOver={(e) => e.preventDefault()}
                    onDrop={handleDrop}
                >
                    <input
                        type="file"
                        multiple
                        onChange={handleFileChange}
                        style={{ display: 'none' }}
                        id="fileInput"
                    />
                    <label htmlFor="fileInput" className="vb_contact_file_text text-center">
                        <u>Add file</u> or drop files here
                    </label>
                </div>
                {files.length > 0 && (
                    <ul className="list-unstyled mt-2 px-2">
                        {files.map((file, index) => (
                            <li className="vb_contact_file_text" key={index} style={{padding: 0}}>
                                {file.name}
                            </li>
                        ))}
                    </ul>
                )}
            </div>
        </div>
    );
};

export default FileUpload;
