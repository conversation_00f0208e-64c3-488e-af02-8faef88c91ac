import {useQuery} from "react-query";
import {getAccessToken, showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getClientOrders} from "../../api/orders/getClientOrders.js";
import orderTransformer from "../../api/orders/transformer/orderTransformer.js";

export const useClientOrders = ({query, id}) => {

    const { data: orders, loading: orderLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['client-orders', ...query],
            queryFn: () => getClientOrders(query),
            onError: showError,
            enabled: !!getAccessToken(),
            refetchOnWindowFocus: false,
        }),
        transform: (data) => orderTransformer(data, id),
    });

    return { orders, orderLoading};
};