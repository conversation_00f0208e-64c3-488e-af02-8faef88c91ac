import {getBlogCategories} from "../../api/blog/getBlogCategories.js";

import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";

export const useBlogCategories = () => {
    const { data: categories, loading: categoriesLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['blog-categories'],
            queryFn: () => getBlogCategories(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { categories, categoriesLoading};
};