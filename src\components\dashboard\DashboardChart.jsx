import {useProfile} from "../../hooks/auth/useProfile.js";

const baseChartData = [
    { month: "Jan" }, { month: "Feb" }, { month: "Mar" }, { month: "Apr" },
    { month: "May" }, { month: "Jun" }, { month: "Jul" }, { month: "Aug" },
    { month: "Sep" }, { month: "Oct" }, { month: "Nov" }, { month: "Dec" }
];

export default function DashboardChart({ data, setYearState, yearState }) {

    const {user} = useProfile();

    const sellerRevenues = data.map(item => typeof item.sellerRevenue === "number" ? item.sellerRevenue : 0);

    const maxRevenue = sellerRevenues.length > 0 ? Math.max(...sellerRevenues) : 0;

    const roundedMax = Math.ceil(maxRevenue / 200) * 200 || 200;

    const graphScores = Array.from({ length: 6 }, (_, i) =>
        `$${(roundedMax - i * (roundedMax / 5)).toFixed(0)}`
    );

    const chartData = baseChartData.map((entry, idx) => {
        const monthNum = idx + 1;
        const match = data.find(item => item._id.month === monthNum);

        if (match) {
            const percentHeight = Math.round((match.sellerRevenue / roundedMax) * 100);
            return {
                ...entry,
                height: `${percentHeight - 6}%`,
                showValue: true,
                value: `$${(match.sellerRevenue ?? 0).toFixed(2)}`,
                highlight: match.sellerRevenue === maxRevenue // Highlight the top one
            };
        }

        return {
            ...entry,
            height: "0%",
            showValue: false,
            value: ""
        };
    });

    const yearJoined = new Date(user.createdAt).getFullYear();

    return (
        <div className="col-12 col-lg col-xl-9 dash_cont_con mb-3 mb-xl-0">
            <div className="d-flex align-items-center gap-1 mb-4">
                <p className="dash_head">Monthly sales: </p>
                <select className="dash_chart_sel" onChange={e => setYearState(e.target.value)} value={yearState}>
                    {Array.from({ length: new Date().getFullYear() - yearJoined + 1 }, (_, i) => {
                        const year = yearJoined + i;
                        return (
                            <option key={year} value={year}>
                                {year}
                            </option>
                        );
                    })}
                </select>
            </div>
            <div className="d-flex gap-4 align-items-start">
                <div className="col-auto dash_graph_score_con d-flex flex-column justify-content-between">
                    {graphScores.map((score, idx) => (
                        <p className="dash_graph_score" key={idx}>{score}</p>
                    ))}
                </div>
                <div className="col graph_chart_cont text-center d-flex gx-0 gap-3 overflow-auto">
                    {chartData.map((item, index) => (
                        <div className="col-auto" key={index}>
                            <div className="dash_graph_con d-flex flex-column justify-content-end align-items-center mb-2">
                                <div
                                    className="dash_graph_cont position-relative"
                                    style={{ height: item.height }}
                                >
                                    {item.showValue && (
                                        <span className="dash_graph_num position-absolute">
                                            {item.value}
                                        </span>
                                    )}
                                    <div
                                        className="dash_graph position-relative"
                                        style={{
                                            backgroundImage: "url(./assets/images/graph_candle.svg)",
                                        }}
                                    >
                                        <div className="dash_graph_line"></div>
                                        <div
                                            className={`position-absolute dash_graph_col ${
                                                item.highlight ? "" : "d-none"
                                            }`}
                                        ></div>
                                    </div>
                                </div>
                                {!item.height || item.height === "0%" ? (
                                    <div className="dash_graph_line2"></div>
                                ) : null}
                            </div>
                            <p className="dash_graph_score">{item.month}</p>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    );
}
