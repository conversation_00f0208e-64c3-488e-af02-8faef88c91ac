import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getGiveAway} from "../../api/give-away/getGiveAway.js";

export const useGiveAway = () => {
    const { data: giveAway, loading: giveAwayLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['give-away'],
            queryFn: () => getGiveAway(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data[0],
    });

    return { giveAway, giveAwayLoading};
};