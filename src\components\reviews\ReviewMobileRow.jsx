import {Link} from "react-router-dom";
import CustomDropdown from "../../pages/componets/utility/CustomDropdown.jsx";
import {formatDateTime} from "../../vbrae-utils/lib/time.js";

export default function ReviewMobileRow({orderNumber, index, user, comment, offerName, createdAt, stars, setData, reviewId, offerId, sellerResponse}){
    const [p1, p2] = formatDateTime(createdAt)
    return (
        <div className="mob_table_con position-relative mb-3">
            <div className="col row gx-4 gy-3">
                <div className="col-6">
                    <label className="mob_table_head">Id</label>
                    <Link className="mob_table_data">#{index + 1}</Link>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">Order ID</label>
                    <p className="mob_table_data">{orderNumber}</p>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">Buyer</label>
                    <p className="mob_table_data blue">{user}</p>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">Rating</label>
                    <p className="mob_table_data bold">
                          <span className="acct_table_data_icon blue">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="currentColor"
                                  d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                              />
                            </svg>
                          </span>
                        {stars.toFixed(1)}
                    </p>
                </div>
                <div className="col-12">
                    <label className="mob_table_head">Latest Reviews</label>
                    <p className="mob_table_data">
                        {comment}
                    </p>
                </div>

                <div className="col-12">
                    <label className="mob_table_head">Products</label>
                    <p className="mob_table_data blue">
                        {offerName}
                    </p>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">Date</label>
                    <p className="mob_table_data">{p1} / {p2}</p>
                </div>
            </div>
            <div className="mob_table_icon_con position-absolute">
                <div className="col-auto position-relative">
                    <CustomDropdown
                        trigger={() => (
                            <span className="mob_table_data_icon">
                              <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="M12 3c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 14c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-7c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2"
                                />
                              </svg>
                            </span>
                        )}
                        content={
                            <div className="acct_table_drop_cont">
                                {sellerResponse ? null : <Link onClick={()=> setData({reviewId, offerId, isOpen: true, comment})}>
                                    <p className="acct_table_drop_link">Reply</p>
                                </Link>}
                                <Link>
                                    <p className="acct_table_drop_link">Delete</p>
                                </Link>
                                <Link>
                                    <p className="acct_table_drop_link">
                                        To Archive
                                    </p>
                                </Link>
                            </div>
                        }
                    />
                </div>
            </div>
        </div>
    )
}