import {Link} from "react-router-dom";
import CustomDropdown from "../../pages/componets/utility/CustomDropdown.jsx";
import {formatDateTime} from "../../vbrae-utils/lib/time.js";

export default function SaleMobileRow({_id, orderIncrementId, reservationId, createdAt, updatedAt, items, status}){

    const [createdA, createdB] = formatDateTime(createdAt);
    const [updatedA, updatedB] = formatDateTime(updatedAt);
    const orderOffers = items.map(item => item.offerDetails.name).join(" / ");
    const totalPrice = items.reduce((total, currentValue)=> total + (currentValue.offerDetails.expectedPrice * currentValue.quantity), 0);

    return (
        <div className="mob_table_con position-relative mb-3">
            <div className="col row gx-4 gy-3">
                <div className="col-12 d-flex gap-3 align-items-center">
                    <Link to={`/account/sales-details/${_id}`}>
                        <p className="col mob_table_data">
                            {orderOffers}
                        </p>
                    </Link>
                </div>

                <div className="col-6">
                    <label className="mob_table_head">IWTR</label>
                    <p className="mob_table_data">€{totalPrice.toFixed(2)}</p>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">Bid</label>
                    <p className="mob_table_data">-</p>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">
                        ORDER INCREMENT ID
                    </label>
                    <p className="mob_table_data bold">{orderIncrementId}</p>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">RESERVATION ID</label>
                    <p className="mob_table_data">
                        {reservationId}
                    </p>
                </div>

                <div className="col-6">
                    <label className="mob_table_head">STATUS</label>
                    <span
                        className={`acct_table_data_tag text-capitalize ${status === "completed" ? 'green' : (status === "canceled" ? "red" : "blue")}`}>
                          {status}
                        </span>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">CREATED</label>
                    <p className="mob_table_data">{createdA} {createdB}</p>
                </div>

                <div className="col-6">
                    <label className="mob_table_head">RELEASED </label>
                    <p className="mob_table_data">{updatedA} {updatedB}</p>
                </div>
            </div>
            <div className="mob_table_icon_con position-absolute">
                <div className="col-auto position-relative">
                    <CustomDropdown
                        trigger={() => (
                            <span className="mob_table_data_icon">
                              <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="M12 3c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 14c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-7c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2"
                                />
                              </svg>
                            </span>
                        )}
                        content={
                            <div className="acct_table_drop_cont">
                                <Link>
                                    <p className="acct_table_drop_link">Delete</p>
                                </Link>
                                <Link>
                                    <p className="acct_table_drop_link">
                                        To Archive
                                    </p>
                                </Link>
                                <Link>
                                    <p className="acct_table_drop_link">Other</p>
                                </Link>
                            </div>
                        }
                    />
                </div>
            </div>
        </div>
    )
}