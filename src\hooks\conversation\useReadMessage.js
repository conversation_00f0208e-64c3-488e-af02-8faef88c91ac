import {useQuery, useQueryClient} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {useSearchParams} from "react-router-dom";
import {patchMessage} from "../../api/conversation/patchMessage.js";

export const useReadMessage = () => {

    const queryClient = useQueryClient();
    const [searchParams] = useSearchParams();
    const conversationId = searchParams.get('conversationId');

    const { refetch } = useQueryFix({
        query: useQuery({
            queryKey: ['messages-read', conversationId],
            queryFn: () => patchMessage({conversationId}),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: !!conversationId,
            onSuccess: () => queryClient.invalidateQueries(['messages', conversationId]),
        }),
        transform: (data) => data,
    });

    return { refetch };
};