import {getBestSellers} from "../../api/offers/getBestSellers.js";

import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";

export const useBestSellers = ({sellerId, query}) => {
    const { data: offers, loading: offersLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['game-item', 'best-seller-offers', query],
            queryFn: () => getBestSellers({sellerId, query}),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.bestSellers,
    });

    return { offers, offersLoading};
};