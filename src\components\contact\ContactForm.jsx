import useContactForm from "../../hooks/contact/useContactForm.js";
import FileUpload from "../ticket/FileUpload.jsx";
import Spinner from "../common/Spinner.jsx";

export default function ContactForm(){

    const {formik} = useContactForm();

    return (
        <form onSubmit={formik.handleSubmit} className="col mt-4">
            <div className="col mb-3">
                <p className="vb_head mb-2">Your email address</p>
                <input
                    type="email"
                    className="vb_contact_input"
                    placeholder=""
                    {...formik.getFieldProps("email")}
                />
                {formik.touched.email &&
                    <small className="main_footer_copy text-danger">{formik.errors.email}</small>}
            </div>
            <div className="col mb-3">
                <p className="vb_head mb-2">Description</p>
                <textarea
                    className="vb_contact_input h-auto"
                    id="description"
                    cols="30"
                    rows="5"
                    {...formik.getFieldProps("description")}
                />
                {formik.touched.description &&
                    <small className="main_footer_copy text-danger">{formik.errors.description}</small>}
            </div>
            <div className="col mb-3">
                {formik.isSubmitting ? <Spinner /> : <FileUpload onFilesSelected={files=> formik.setFieldValue("attachments", files)}/>}
            </div>

            <button
                disabled={formik.isSubmitting}
                className="col-12 col-md-auto vb_contact_btn">
                {formik.isSubmitting ? "Processing..." : "Send Message" }
            </button>
        </form>
    )
}