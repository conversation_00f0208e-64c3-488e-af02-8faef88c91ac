import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getWishlist} from "../../api/wishlist/getWishlist.js";

export const useWishlist = ({query}) => {
    const { data: offers, loading: offersLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['game-item','wishlist', query],
            queryFn: () => getWishlist({query}),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { offers, offersLoading};
};