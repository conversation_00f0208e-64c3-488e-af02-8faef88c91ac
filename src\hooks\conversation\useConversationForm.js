import * as Yup from 'yup';
import {useMutation} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {postConversation} from "../../api/conversation/postConversation.js";
import {useParams, useSearchParams} from "react-router-dom";
import {postMessage} from "../../api/conversation/postMessage.js";

const schema = Yup.object().shape({
    content: Yup.string().required('Body is required'),
});

export default function useConversationForm({sellerId}) {

    const {id} = useParams();
    const [_, setSearchParams] = useSearchParams();

    const initialValues = {
        content: '',
    }

    const { mutateAsync } = useMutation(postConversation, {
        onError: (error)=> showError(error),
    });

    const { mutateAsync:updateAsync } = useMutation(postMessage, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: schema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync({ offerId: id, sellerId, type:"offer" });
            setSearchParams({ conversationId: response._id})
            await updateAsync({...values, conversationId: response._id, entityType: "conversation-offer"})
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess("Request submitted successfully.");
            }
        },
    });

    return { formik };
}
