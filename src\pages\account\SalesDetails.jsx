/* eslint-disable react/prop-types */
import {Link, useParams} from "react-router-dom";
import AccountSideBar from "../componets/account/AccountSideBar";
import MainHeader from "../componets/MainHeader";
import MainFooter from "../componets/MainFooter";
import {useSales} from "../../hooks/sales/useSales.js";
import Spinner from "../../components/common/Spinner.jsx";
import OrderCard from "../../components/sales/OrderCard.jsx";

export default function SalesDetails() {
  const breadCrums = [
    {
      title: "Sales",
      url: "/account/sales",
    },
    {
      title: "Sale #20074",
      url: "/account/sales-details",
    },
  ];

  const breadCrumsList = breadCrums?.map((item, index) => {
    return (
      <Link to={item.url} key={index}>
        <p className="crumb_link">/ {item.title}</p>
      </Link>
    );
  });

  return (
    <>
      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={"sales"} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadCrums}
            activeLink={"sales"}
          />

          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <div className=" d-lg-none justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-2">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                {breadCrumsList}
              </div>
            </div>

            <RenderSaleDetails />

            <div className="col d-lg-none mt-4">
              <MainFooter />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

const RenderSaleDetails = () => {
  const {id} = useParams();
  const {salesData, salesLoading} = useSales({id});

  if(salesLoading || !salesData) return <Spinner />
  return (
      <div className="acct_cont">
        <p className="acct_offer_title ">Sale {salesData.orderNumber}</p>

        <hr className="acct_fil_line my-3"/>

        <div className="col d-flex flex-wrap gap-3 mb-5">
          <OrderCard {...salesData}/>
          <div className="col-12 col-xl-4 ">
            <div className="col acct_offer_cont dark mb-3">
              <p className="acct_head_smm mb-4">Order summary</p>

              <p className="cart_sum_text d-flex justify-content-between align-items-center mb-2">
                Subtotal ({salesData.items.length} item) <span className="white">${salesData.subtotal.toFixed(2)}</span>
              </p>
              <p className="cart_sum_text d-flex justify-content-between align-items-center mb-2">
                Shipping <span className="white">$0</span>
              </p>
              <p className="cart_sum_text d-flex justify-content-between align-items-center mb-2">
                Fees <span className="white">${salesData.serviceFee.toFixed(2)}</span>
              </p>

              <p className="cart_sum_head d-flex justify-content-between align-items-center mt-4">
                Total <span>${salesData.total.toFixed(2)}</span>
              </p>
            </div>
            <div className="d-flex">

            </div>
          </div>
        </div>
      </div>
  )
}