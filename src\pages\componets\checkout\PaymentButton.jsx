import {useCreatePayment} from "../../../hooks/payment/useCreatePayment.js";
import {useProfile} from "../../../hooks/auth/useProfile.js";
import {useState} from "react";
import Spinner from "../../../components/common/Spinner.jsx";

export default function PaymentButton({ amount, method }) {
    const { viewCheckoutUrl } = useCreatePayment({ amount });
    const { user } = useProfile();
    const [error, setError] = useState('');

    const handleClick = () => {

        if (!user?.address.length > 0) {
            setError('Billing Address is required before placing the order.');
            return;
        }

        if (amount > 0 && method === 'swedbank') {
            setError('');
            window.location.href = viewCheckoutUrl;
        }
    };

    if(!viewCheckoutUrl) return <Spinner />

    return (
        <div className="mb-3">
            <button
                type="button"
                onClick={handleClick}
                className="col-12 col-md-10 cart_btn2 mx-auto"
            >
                Pay and place an order
            </button>
            {error &&  <small className="main_footer_copy text-danger d-block">{error}</small>}
        </div>
    );
}
