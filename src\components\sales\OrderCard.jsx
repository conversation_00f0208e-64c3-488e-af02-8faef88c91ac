import OrderAccordion from "../../pages/componets/utility/OrderAccordion.jsx";
import {useEffect, useState} from "react";
import {formatDateTime} from "../../vbrae-utils/lib/time.js";
import {useProfile} from "../../hooks/auth/useProfile.js";
import {useSearchParams} from "react-router-dom";
import {NewLicenseModal} from "../../pages/componets/modals/NewLicenceModal.jsx";
import {usePatchOrder} from "../../hooks/orders/usePatchOrder.js";
import Spinner from "../common/Spinner.jsx";

/* eslint-disable react/prop-types */
export default function OrderCard({status, items, createdAt, updatedAt, userDetails}) {

    const [open, setOpen] = useState(false);
    const [allKeys, setAllKeys] = useState(false);
    const [keysState, setKeysState] = useState(items.map(item=> ({
        instantDelivery: item.offerDetails.instantDelivery,
        offerId: item.offerDetails._id,
        keys: item.keys,
    })));

    const { orderPatch, patchLoading } = usePatchOrder({ offers: keysState, action: "completed" });

    const {user} = useProfile();
    const [_, setSearchParams] = useSearchParams();

    const handleSubmit = (value, idx) => {
        setKeysState(prevState => {
            const updated = prevState.map((k, i) =>
                i === idx ? { ...k, keys: value } : k
            );
            const needsKeys = updated.filter(k => !k.instantDelivery);
            const allKeysEntered = needsKeys.every(k => k.keys.length > 0);

            if (allKeysEntered) setAllKeys(true);
            return updated;
        });
    };

    useEffect(() => {
        if(allKeys) orderPatch().finally(() => {
            setOpen(false);
            setAllKeys(false);
        });
    }, [allKeys]);

    const totalItems = items.reduce((total, currentValue)=> total + currentValue.quantity, 0);
    const orderOffers = items.map(item => item.offerDetails.name).join(" / ");
    const [createdA, createdB] = formatDateTime(createdAt);
    const [updatedA, updatedB] = formatDateTime(updatedAt);

    return (
        <>
            {open && <NewLicenseModal setOpen={setOpen} keysState={keysState} onSubmit={handleSubmit}/>}
            <div className="col-12 col-xl acct_offer_cont">
                <div className="col d-flex flex-wrap gap-3 justify-content-between align-items-center mb-3">
                    <div className="col-12 col-md-auto d-flex align-items-center gap-3">
                        <img
                            src={items[0].offerDetails.coverImage}
                            alt=""
                            className="acct_sale_img"
                        />
                        <div className="col">
                            <p className="acct_head_s">
                                {orderOffers}
                            </p>
                            <p className="acct_offer_text_big">$19.98</p>
                        </div>
                    </div>
                    <button
                        type="button"
                        className="acct_offer_btn2 d-flex gap-2 align-items-center">
                      <span className="icon">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <path
                              fill="currentColor"
                              fillRule="evenodd"
                              d="M4 3a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v18a1 1 0 0 1-1.65.76l-1.033-.885a1 1 0 0 0-1.301 0l-1.032.884a1 1 0 0 1-1.302 0l-1.031-.884a1 1 0 0 0-1.302 0l-1.031.884a1 1 0 0 1-1.302 0l-1.032-.884a1 1 0 0 0-1.301 0l-1.032.884A1 1 0 0 1 4 21zm5 3a1 1 0 0 0 0 2h6a1 1 0 1 0 0-2zm0 4a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2zm1 5a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2h-4a1 1 0 0 1-1-1"
                              clipRule="evenodd"></path>
                        </svg>
                      </span>
                        Invoice
                    </button>
                </div>

                <div className="col row row-cols-2 row-cols-md-4 g-4">
                    <div className="col">
                        <p className="acct_head_sm mb-1">Status</p>
                        <span className={`acct_table_data_tag text-capitalize ${status === "completed" ? "green" : (status === "canceled" ? "red" : "blue")}`}>
                        {status}
                      </span>
                    </div>
                    <div className="col">
                        <p className="acct_head_sm mb-1">Payment Status</p>
                        <p className="acct_head_s">Payment Received</p>
                    </div>
                    <div className="col">
                        <p className="acct_head_sm mb-1">Payment Method </p>
                        <p className="acct_head_s">Ecompay</p>
                    </div>
                    <div className="col">
                        <p className="acct_head_sm mb-1">Quantity</p>
                        <p className="acct_head_s">{totalItems}</p>
                    </div>
                    <div className="col">
                        <p className="acct_head_sm mb-1">Buyer</p>
                        {user && <p className="acct_head_s blue">{userDetails.name}</p>}
                    </div>
                    <div className="col">
                        <p className="acct_head_sm mb-1">Date</p>
                        <p className="acct_head_s">{createdA} / {createdB}</p>
                    </div>
                    <div className="col">
                        <p className="acct_head_sm mb-1">Update</p>
                        <p className="acct_head_s">{updatedA} / {updatedB}</p>
                    </div>
                </div>

                {patchLoading ? <div className="d-flex justify-content-end"><Spinner /></div> : <OrderAccordion
                    isActive={false}
                    trigger={({isOpen, ToggleAccordion}) => {
                        return (<div className="col">
                                <div className="col col-md-auto d-flex gap-4 justify-content-end align-items-center">
                                    <div
                                        className="acct_offer_arrow d-flex gap-1 justify-content-center align-items-center"
                                        role="button"
                                        onClick={() => ToggleAccordion()}>
                                        {patchLoading ? "Processing..." : (isOpen ? "Close" : "Show keys")}
                                        <span className="icon">
                              {isOpen ? (
                                  <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="1em"
                                      height="1em"
                                      viewBox="0 0 24 24">
                                      <path
                                          fill="none"
                                          stroke="currentColor"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="m5 15l7-7l7 7"></path>
                                  </svg>
                              ) : (
                                  <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="1em"
                                      height="1em"
                                      viewBox="0 0 24 24">
                                      <path
                                          fill="none"
                                          stroke="currentColor"
                                          strokeLinecap="round"
                                          strokeLinejoin="round"
                                          strokeWidth={2}
                                          d="m19 9l-7 7l-7-7"></path>
                                  </svg>
                              )}
                            </span>
                                    </div>
                                </div>
                            </div>
                        )
                    }}
                    content={
                        <>
                            {items.map((item, idx) => (
                                <div className="col" key={item._id}>
                                    <hr className="acct_fil_line2 my-3"/>
                                    <div className="col d-flex gap-3 justify-content-between align-items-center">
                                        <div className="col">
                                            <p className="acct_head_s">
                                                {item.offerDetails.name} {item.offerDetails.region} ({item.quantity})
                                            </p>
                                            <p className="acct_offer_text_big">${item.offerDetails.expectedPrice}</p>
                                        </div>

                                        <button
                                            type="button"
                                            className="col-auto acct_offer_btn3"
                                            onClick={() => {
                                                setSearchParams({offer: item.offerDetails._id, idx});
                                                setOpen(true)
                                            }}>
                                            {item.keys.length > 0 ? "View Key" : "Attach Keys"}
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </>
                    }
                />}
            </div>
        </>
    )
}