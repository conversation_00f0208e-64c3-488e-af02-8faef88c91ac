import * as Yup from 'yup';
import {useMutation} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {uploadUrl} from "../../api/uploadUrl.js";
import {postContact} from "../../api/contact/postContact.js";

const schema = Yup.object().shape({
    email: Yup.string().email('Invalid email').required('Email is required'),
    description: Yup.string().required('Subject is required'),
    attachments: Yup.array().of(Yup.mixed()).nullable(),
});

export default function useContactForm() {
    const initialValues = {
        email: '',
        description: '',
        attachments: []
    }

    const uploadImage = async (image) => {
        if (image instanceof File) {
            const fileType = image.name.split('.').pop()?.toLowerCase() || 'unknown';
            const resignedResponse = await uploadUrl({ name: image.name, fileType });
            const { url: resignedUrl, path: filePath } = resignedResponse;

            await fetch(resignedUrl, {
                method: 'PUT',
                headers: { 'Content-Type': image.type, 'x-amz-acl': 'public-read' },
                body: image,
            });

            return filePath;
        }
        return image;
    };

    const { mutateAsync } = useMutation(postContact, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: schema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);

            const imageUrls = await Promise.all(values.attachments.map(async (attachment) => await uploadImage(attachment)))

            const response = await mutateAsync({...values, attachments: imageUrls});
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess("Request submitted successfully.");
            }
        },
    });

    return { formik };
}
