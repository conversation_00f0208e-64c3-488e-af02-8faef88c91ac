import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getShops} from "../../api/shop/getShops.js";

export const useShops = () => {

    const { data: shops, loading: shopsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['shops'],
            queryFn: () => getShops(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { shops, shopsLoading};
};