import FaqAccordion from "../../pages/componets/FaqAccordion.jsx";
import {useFaq} from "../../hooks/faq/useFaq.js";
import Spinner from "../common/Spinner.jsx";
import {Link} from "react-router-dom";

export default function FAQ(){

    const {faq, faqLoading} = useFaq();

    if(faqLoading || !faq) return <Spinner />;

    return (
        <div className="col mb-5">
            <div className="col d-flex gap-3 justify-content-between align-items-center mb-4">
                <p className="vb_title_sm">FAQ</p>
                <p className="vb_faq_head text-end">
                    Need help? <br className="d-md-none"/> Visit our{" "}
                    <Link to="/help-center"><u>Help Center</u></Link>
                </p>
            </div>

            <div className="col">
                {faq.map(item=> (
                    <FaqAccordion
                        key={item._id}
                        isActive={false}
                        triggerText={item.title}
                        contentText={item.content}
                    />
                ))}
            </div>
        </div>
    )
}