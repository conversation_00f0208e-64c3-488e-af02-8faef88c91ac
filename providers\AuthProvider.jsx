import {createContext} from "react";
import app from "./Firebase/Firebase.init.jsx";
import {getAuth, signInWithPopup} from "firebase/auth";
import {NotificationListener} from "../src/components/NotificationListener.js";
import {MessageListener} from "../src/components/conversations/MessageListener.js";
import {useProfile} from "../src/hooks/auth/useProfile.js";

const initialAuthContextValue = {
    registerWithGoogle: () => Promise.reject(new Error("Auth context not initialized")),
    registerWithFacebook: () => Promise.reject(new Error("Auth context not initialized")),
};

export const AuthContext = createContext(initialAuthContextValue);

export function AuthProvider({ children }) {

    const {user} = useProfile();

    const auth = getAuth(app);

    const registerWithGoogle = (provider) => {
        return signInWithPopup(auth, provider);
    };

    const registerWithFacebook = (provider) => {
        return signInWithPopup(auth, provider);
    };

    const contextValue = {
        registerWithGoogle,
        registerWithFacebook
    };

    return (
        <AuthContext.Provider value={contextValue}>
            {user && <NotificationListener />}
            {user && <MessageListener />}
            {children}
        </AuthContext.Provider>
    );
}

export default AuthProvider;