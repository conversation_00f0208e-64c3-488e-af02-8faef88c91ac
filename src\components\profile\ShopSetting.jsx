import {passwordKeys, passwordRequirements} from "../../constant/auth.js";

export default function ShopSetting({formik}){
    return (
        <div className="col pro_tab_cont">
            <p className="acct_offer_title gray mb-3">Shop Settings</p>

            <div className="col d-flex flex-wrap gap-3">
                <div className="col">
                    <div className="d-md-flex gap-3">
                        <div className="col mb-3">
                            <label className="acct_offer_label green mb-2">
                                Shop Name (required)
                            </label>
                            <div className="acct_box filled">
                                <input
                                    type="text"
                                    name="shopName"
                                    className="input_box "
                                    {...formik.getFieldProps("shopName")}
                                />
                                {formik.touched.shopName &&
                                    <small className="main_footer_copy text-danger">{formik.errors.shopName}</small>}
                            </div>
                        </div>
                        <div className="col mb-3"></div>
                    </div>

                    <div className="col mb-4">
                        <div className="form-check form-switch d-flex gap-3 align-items-center mb-2">
                            <input
                                className="form-check-input"
                                type="checkbox"
                                role="switch"
                                id="flexSwitchCheckChecked"
                                checked={formik.values.vacationMode}
                                onChange={e => formik.setFieldValue("vacationMode", e.target.checked)}
                            />
                            <label
                                className="acct_offer_title small"
                                htmlFor="flexSwitchCheckChecked">
                                Vacation mode
                            </label>
                        </div>
                        <p className="acct_offer_text">
                            Pause your game and notifications. Enjoy your break!
                        </p>
                    </div>

                    <div className="col mb-4">
                        <p className="acct_offer_title small d-flex gap-3 align-items-center mb-4">
                          <span className="icon">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="none"
                                  stroke="currentColor"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="m5 15l7-7l7 7"></path>
                            </svg>
                          </span>
                            Change your password
                        </p>

                        <div className="col">
                            <div className="col mb-3">
                                <label className="acct_offer_label mb-2">
                                    Current password
                                </label>
                                <div className="acct_box">
                                    <input
                                        type="text"
                                        name="currentPassword"
                                        className="input_box "
                                        placeholder=""
                                        {...formik.getFieldProps("currentPassword")}
                                    />
                                    {formik.touched.currentPassword &&
                                        <small className="main_footer_copy text-danger">{formik.errors.currentPassword}</small>}
                                </div>
                            </div>
                            <div className="col mb-3">
                                <label className="acct_offer_label mb-2">
                                    New password
                                </label>
                                <div className="acct_box">
                                    <input
                                        type="text"
                                        name="newPassword"
                                        className="input_box "
                                        placeholder=""
                                        {...formik.getFieldProps("newPassword")}
                                    />
                                    {formik.touched.newPassword &&
                                        <small className="main_footer_copy text-danger">{formik.errors.newPassword}</small>}
                                </div>
                            </div>

                            <div className="col mb-3">
                                <label className="acct_offer_label mb-3">
                                    Your password must have
                                </label>
                                <div className="col row row-cols-md-2">
                                    {passwordKeys.map((item, index) => (
                                        <div className="col d-flex align-items-center gap-2 mb-2" key={index}>
                                            <input
                                                className="col-auto auth_check"
                                                type="checkbox"
                                                checked={passwordRequirements(formik.values.newPassword ?? "")[item.key]}
                                                id={item.id}
                                            />
                                            <label className="auth_check_text" htmlFor={item.id}>
                                                {item.label}
                                            </label>
                                        </div>
                                    ))}
                                </div>
                            </div>
                            <div className="d-md-flex gap-3 align-items-end">
                                <div className="col mb-3">
                                    <label className="acct_offer_label mb-2">
                                        Repeat password
                                    </label>
                                    <div className="acct_box">
                                        <input
                                            type="text"
                                            name="repeatPassword"
                                            className="input_box "
                                            placeholder=""
                                            {...formik.getFieldProps("repeatPassword")}
                                        />
                                        {formik.touched.repeatPassword &&
                                            <small className="main_footer_copy text-danger">{formik.errors.repeatPassword}</small>}
                                    </div>
                                </div>
                                <div className="col d-flex gap-2 mb-3">
                                    <button
                                        type="button"
                                        className="col acct_offer_btn2">
                                        Cancel
                                    </button>
                                    <button onClick={formik.handleSubmit} type="button"
                                        className="col acct_offer_btn1">
                                        {formik.isSubmitting ? "Processing..." : "Change"}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="col-12 col-xl-4"></div>
            </div>
        </div>
    )
}