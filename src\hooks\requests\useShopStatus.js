import {useQuery} from "react-query";
import {getAccessToken, useQueryFix} from "../../vbrae-utils/index.js";
import {getShopStatus} from "../../api/requests/getShopStatus.js";

export const useShopStatus = () => {

    const { data, loading } = useQueryFix({
        query: useQuery({
            queryKey: ['shops-status'],
            queryFn: () => getShopStatus(),
            enabled: !!getAccessToken(),
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { data, loading };
};