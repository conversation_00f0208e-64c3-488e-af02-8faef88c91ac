import {Link} from "react-router-dom";
import CustomDropdown from "../../pages/componets/utility/CustomDropdown.jsx";

export default function OfferRow({
                                     template,
                                     stock,
                                     _id,
                                     expectedPrice,
                                     customerPays,
                                     isHot,
                                     setIsRetailPriceOpen,
                                     setProductToDelete,
                                     setOfferToUpdate,
                                     sold,
                                     lowestPrice,
                                     last24HoursSales,
                                     lastMonthSales,
                                     lastWeekSales

}){

    const openRetailPrice = () => {
        setIsRetailPriceOpen({
            value: true,
            _id: _id,
            expectedPrice: expectedPrice,
            customerPays: customerPays
        })
    }

    return (
        <tr className="acct_table_row mb-2">
            <td className="acct_table_data">
                <img
                    src={template.coverImage}
                    alt=""
                    className="acct_table_data_img"
                />
            </td>
            <td className="acct_table_data ">
                <Link
                    to={`/account/offer-edit/${_id}`}
                    className="acct_table_data">
                    {template.templateName}
                </Link>
            </td>
            <td className="acct_table_data">{stock} / {sold}</td>
            <td className="acct_table_data text-end">
                24H: {last24HoursSales} <br/> 7D: {lastWeekSales} <br/> 30D: {lastMonthSales}
            </td>
            <td className="acct_table_data">
                {lowestPrice ? `€${lowestPrice.toFixed(2)}` : '-'}
            </td>
            <td className="acct_table_data">1</td>
            <td className="acct_table_data">
                {isHot ? <span className="game_badge type2 me-1 mb-1">
                <svg
                    className="me-1"
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 256 256">
                  <path
                      fill="currentColor"
                      d="M143.38 17.85a8 8 0 0 0-12.63 3.41l-22 60.41l-24.16-23.41a8 8 0 0 0-11.93.89C51 87.53 40 116.08 40 144a88 88 0 0 0 176 0c0-59.45-50.79-108-72.62-126.15m40.51 135.49a57.6 57.6 0 0 1-46.56 46.55a7.7 7.7 0 0 1-1.33.11a8 8 0 0 1-1.32-15.89c16.57-2.79 30.63-16.85 33.44-33.45a8 8 0 0 1 15.78 2.68Z"
                  />
                </svg>
                Hot
              </span> : "-"}
            </td>
            <td className="acct_table_data">
                <span className="acct_table_data_tag">Inactive</span>
            </td>
            <td className="acct_table_data">
                          <span className="acct_table_data_tag_con d-inline-flex gap-1 align-items-center">
                            ${expectedPrice}
                              <span className="acct_table_data_tag active" onClick={openRetailPrice}>
                              ${customerPays}
                            </span>
                          </span>
            </td>
            <td className="acct_table_data position-relative">
                <CustomDropdown
                    trigger={() => (
                        <span className="acct_table_data_icon">
                                  <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="1em"
                                      height="1em"
                                      viewBox="0 0 24 24">
                                  <path
                                      fill="currentColor"
                                      d="M12 3c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 14c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-7c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2"
                                  />
                                  </svg>
                                  </span>
                    )}
                    content={
                        <div className="acct_table_drop_cont">
                            <Link onClick={() => setProductToDelete({_id: _id})}>
                                <p className="acct_table_drop_link">Delete</p>
                            </Link>
                            <Link onClick={() => setOfferToUpdate({
                                _id: _id,
                                active: false,
                            })}>
                                <p className="acct_table_drop_link">
                                    To Archive
                                </p>
                            </Link>
                            <Link>
                                <p className="acct_table_drop_link">Other</p>
                            </Link>
                            <p
                                className="acct_table_drop_link"
                                role="button"
                                onClick={openRetailPrice}>
                                Set Retail Price
                            </p>
                        </div>
                    }
                />
            </td>
        </tr>
    )
}