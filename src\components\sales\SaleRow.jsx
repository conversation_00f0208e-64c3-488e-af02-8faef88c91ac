import {Link} from "react-router-dom";
import CustomDropdown from "../../pages/componets/utility/CustomDropdown.jsx";
import {formatDateTime} from "../../vbrae-utils/lib/time.js";

export default function SaleRow({_id, orderIncrementId, reservationId, createdAt, updatedAt, items, status}) {
    const [createdA, createdB] = formatDateTime(createdAt);
    const [updatedA, updatedB] = formatDateTime(updatedAt);
    const orderOffers = items.map(item => item.offerDetails.name).join(" / ");
    const totalPrice = items.reduce((total, currentValue)=> total + (currentValue.offerDetails.expectedPrice * currentValue.quantity), 0);
    return (
        <tr className="acct_table_row" key={_id}>
            <td className="acct_table_data">
                <Link
                    to={`/account/sales-details/${_id}`}
                    className="acct_table_data">
                    {orderOffers}
                </Link>
            </td>
            <td className="acct_table_data">€{totalPrice.toFixed(2)}</td>
            <td className="acct_table_data">-</td>
            <td className="acct_table_data">{orderIncrementId}</td>
            <td className="acct_table_data">
                {reservationId}
            </td>
            <td className="acct_table_data">
                          <span className={`acct_table_data_tag text-capitalize ${status === "completed" ? 'green' : (status === "canceled" ? "red" : "blue")}`}>
                            {status}
                          </span>
            </td>
            <td className="acct_table_data text-nowrap">
                {createdA} <br/> {createdB}
            </td>
            <td className="acct_table_data text-nowrap">
                {updatedA} <br/> {updatedB}
            </td>
            <td className="acct_table_data position-relative">
                <CustomDropdown
                    trigger={() => (
                        <span className="acct_table_data_icon">
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 24 24">
                                  <path
                                      fill="currentColor"
                                      d="M12 3c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 14c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-7c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2"
                                  />
                                </svg>
                              </span>
                    )}
                    content={
                        <div className="acct_table_drop_cont">
                            <Link>
                                <p className="acct_table_drop_link">Delete</p>
                            </Link>
                            <Link>
                                <p className="acct_table_drop_link">
                                    To Archive
                                </p>
                            </Link>
                            <Link>
                                <p className="acct_table_drop_link">Other</p>
                            </Link>
                        </div>
                    }
                />
            </td>
        </tr>
    )
}