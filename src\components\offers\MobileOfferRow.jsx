import {Link} from "react-router-dom";
import CustomDropdown from "../../pages/componets/utility/CustomDropdown.jsx";

export default function MobileOfferRow({
                                           template,
                                           stock,
                                           _id,
                                           expectedPrice,
                                           customerPays,
                                           setIsRetailPriceOpen,
                                           setProductToDelete,
                                           setOfferToUpdate,
                                           sold,
                                            last24HoursSales,
                                            lastWeekSales,
                                            lastMonthSales,
                                            lowestPrice,
                                           isHot,
                                           instantDelivery
                                       }){
    return (
        <div className="mob_table_con position-relative mb-3">
            <div className="col row gx-4 gy-3">
                <div className="col-12 d-flex gap-3 align-items-center">
                    <img
                        src={template.coverImage}
                        alt=""
                        className="col-auto acct_table_data_img"
                    />
                    <Link to={`/account/edit-details${_id}`}>
                        <p className="col mob_table_data">
                            {template.templateName}
                        </p>
                    </Link>
                </div>

                <div className="col-3">
                    <label className="mob_table_head">Stock / Sold</label>
                    <p className="mob_table_data">{stock}/{sold}</p>
                </div>
                <div className="col-3">
                    <label className="mob_table_head">SALES</label>
                    <p className="mob_table_data">
                        24H: {last24HoursSales} <br/> 7D: {lastWeekSales} <br/> 30D: {lastMonthSales}
                    </p>
                </div>
                <div className="col-3">
                    <label className="mob_table_head">LOWEST PRICE</label>
                    <p className="mob_table_data bold">
                        {lowestPrice ? `€${lowestPrice.toFixed(2)}` : '-'}
                    </p>
                </div>
                <div className="col-3">
                    <label className="mob_table_head">POS</label>
                    <p className="mob_table_data">1</p>
                </div>

                <div className="col-6">
                    <label className="mob_table_head">achievement</label>
                    <div className="col d-flex flex-wrap gap-1">
                        {isHot && <span className="game_badge type2 me-1 mb-1">
                            <svg
                                className="me-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 256 256">
                              <path
                                  fill="currentColor"
                                  d="M143.38 17.85a8 8 0 0 0-12.63 3.41l-22 60.41l-24.16-23.41a8 8 0 0 0-11.93.89C51 87.53 40 116.08 40 144a88 88 0 0 0 176 0c0-59.45-50.79-108-72.62-126.15m40.51 135.49a57.6 57.6 0 0 1-46.56 46.55a7.7 7.7 0 0 1-1.33.11a8 8 0 0 1-1.32-15.89c16.57-2.79 30.63-16.85 33.44-33.45a8 8 0 0 1 15.78 2.68Z"
                              />
                            </svg>
                            Hot
                          </span>}
                        {instantDelivery && <span className="game_badge type3 me-1 mb-1">
                            <svg
                                className="me-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 16 16">
                              <path
                                  fill="currentColor"
                                  d="M11.251.068a.5.5 0 0 1 .227.58L9.677 6.5H13a.5.5 0 0 1 .364.843l-8 8.5a.5.5 0 0 1-.842-.49L6.323 9.5H3a.5.5 0 0 1-.364-.843l8-8.5a.5.5 0 0 1 .615-.09z"
                              />
                            </svg>
                            Instant
                          </span>}
                    </div>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">POS</label>
                    <p className="mob_table_data">1</p>
                </div>

                <div className="col-6">
                    <label className="mob_table_head">Booster</label>
                    <span className="acct_table_data_tag">Inactive</span>
                </div>
                <div className="col-6">
                    <label className="mob_table_head">POS</label>
                    <span className="acct_table_data_tag_con d-inline-flex gap-1 align-items-center">
                            ${expectedPrice}
                        <span className="acct_table_data_tag active">
                            ${customerPays}
                          </span>
                        </span>
                </div>
            </div>
            <div className="mob_table_icon_con position-absolute">
                <div className="col-auto position-relative">
                    <CustomDropdown
                        trigger={() => (
                            <span className="mob_table_data_icon">
                              <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="M12 3c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0 14c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2m0-7c-1.1 0-2 .9-2 2s.9 2 2 2s2-.9 2-2s-.9-2-2-2"
                                />
                              </svg>
                            </span>
                        )}
                        content={
                            <div className="acct_table_drop_cont">
                                <Link onClick={() => setProductToDelete({_id: _id})}>
                                    <p className="acct_table_drop_link">Delete</p>
                                </Link>
                                <Link onClick={() => setOfferToUpdate({
                                    _id: _id,
                                    active: false,
                                })}>
                                    <p className="acct_table_drop_link">
                                        To Archive
                                    </p>
                                </Link>
                                <Link>
                                    <p className="acct_table_drop_link">Other</p>
                                </Link>
                                {setIsRetailPriceOpen && <p
                                    className="acct_table_drop_link"
                                    role="button"
                                    onClick={() => setIsRetailPriceOpen({
                                        value: true,
                                        _id: _id,
                                        expectedPrice: expectedPrice,
                                        customerPays: customerPays
                                    })}>
                                    Set Retail Price
                                </p>}
                            </div>
                        }
                    />
                </div>
            </div>
        </div>
    )
}