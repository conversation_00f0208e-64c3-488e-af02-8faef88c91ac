/* eslint-disable react/prop-types */
import useReportForm from "../../../hooks/orders/reports/useReportForm.js";
import {useState} from "react";

export default function ReportModal({ isOpen, onClose, order }) {

  const [keyState, setKeyState] = useState([])

  const {orderId, items} = order;
  const itemsToShow = items.filter(offer=> offer.status === 'completed');

  const {formik} = useReportForm({onClose});

  return (
    <>
      {isOpen && (
        <div className="modal_con d-lg-flex justify-content-center align-items-start align-items-xl-center inset-0">
          <form
            onSubmit={formik.handleSubmit}
            method="post"
            className="col col-lg-10 col-xl-6 modal_cont position-relative">
            <span
              className="dash_level_close position-absolute"
              role="button"
              onClick={() => onClose()}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"
                />
              </svg>
            </span>
            <p className="dash_level_title mb-4">Report a problem </p>

            <div className="col mb-3">
              <p className="auth_label text-uppercase mb-2">Problem Type</p>
              <div className="d-flex gap-4 flex-wrap align-items-start">
                {['Revoked', 'Already Redeemed', 'Other'].map((type, index) => (
                    <div key={type} className="d-flex align-items-center gap-2 mb-3">
                      <input
                          type="radio"
                          className="acct_offer_check"
                          id={`problemType-${index}`}
                          name="problemType"
                          value={type}
                          onChange={formik.handleChange}
                          checked={formik.values.problemType === type}
                      />
                      <label htmlFor={`problemType-${index}`} className="acct_offer_text">
                        {type}
                      </label>
                    </div>
                ))}
                {formik.touched.problemType && formik.errors.problemType && (
                    <small className="main_footer_copy text-danger">{formik.errors.problemType}</small>
                )}
              </div>
            </div>

            <div className="col mb-3">
              <p className="auth_label text-uppercase mb-2">
                Select Item you wish to report
              </p>
              <div className="">
                <select
                    name="offerId"
                    className="acct_sel"
                    value={formik.values.offerId}
                    onChange={(e) => {
                      const selectedOfferId = e.target.value;
                      formik.setFieldValue('offerId', selectedOfferId);
                      const selectedItem = itemsToShow.find(item => item._id === selectedOfferId);
                      if (selectedItem) {
                        setKeyState(selectedItem.keys)
                      }
                    }}
                    onBlur={formik.handleBlur}
                >
                  <option value="" disabled>
                    Select an item
                  </option>
                  {itemsToShow.map((item) => (
                      <option value={item._id} key={item._id}>
                        {item.offerDetails.name}
                      </option>
                  ))}
                </select>

                {formik.touched.offerId && formik.errors.offerId && (
                    <small className="main_footer_copy text-danger">{formik.errors.offerId}</small>
                )}
              </div>
            </div>

            <div className="d-flex gap-3">
              <div className="col mb-3">
                <p className="auth_label text-uppercase mb-2">
                  {"Select Item's Key"}
                </p>
                <div className="">
                  <select
                      name="key"
                      className="acct_sel"
                      value={formik.values.key}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                  >
                    <option value="" disabled>
                      Select key
                    </option>
                    {keyState.map((item, index) => <option value={item} key={index}>{item}</option>)}
                  </select>
                  {formik.touched.key && formik.errors.key && (
                      <small className="main_footer_copy text-danger">{formik.errors.key}</small>
                  )}
                </div>
              </div>

              <div className="col mb-3">
                <p className="auth_label text-uppercase mb-2">
                  Preferred Solution
                </p>
                <div className="">
                  <select
                      name="preferredSolution"
                      className="acct_sel"
                      value={formik.values.preferredSolution}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                  >
                    <option value="">Select a solution</option>
                    <option value="Refund in store Balance">Refund in store balance</option>
                    <option value="Provide new Key">Provide new key</option>
                  </select>
                  {formik.touched.preferredSolution && formik.errors.preferredSolution && (
                      <small className="main_footer_copy text-danger">{formik.errors.preferredSolution}</small>
                  )}
                </div>
              </div>
            </div>

            <div className="col">
              <div className="col mb-3">
                <label
                  htmlFor="comment"
                  className="auth_label text-uppercase mb-2">
                  Comment
                </label>
                <div className="col acct_box">
                  <textarea
                      name="comment"
                      className="input_box"
                      placeholder="Write your message"
                      rows={4}
                      value={formik.values.comment}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                  />
                  {formik.touched.comment && formik.errors.comment && (
                      <small className="main_footer_copy text-danger">{formik.errors.comment}</small>
                  )}
                </div>
              </div>
              <div className="col mb-3">
                <label
                    htmlFor="attachments"
                    className="auth_label text-uppercase mb-2">
                  Attachments <span>(optional)</span>
                </label>

                <div
                    className="col acct_offer_file_con"
                    onClick={() => document.getElementById('attachments-input')?.click()}
                    style={{ cursor: 'pointer' }}
                >
                  <div className="acct_offer_file">
                    <p className="acct_offer_text text-center">
        <span className="bold">
          <u>Add file</u> or drop files here
        </span>
                      <br />
                      Max 3 screenshot files (Only JPG, JPEG, PNG allowed)
                    </p>
                  </div>
                </div>

                <input
                    id="attachments-input"
                    name="attachments"
                    type="file"
                    accept=".jpg,.jpeg,.png"
                    multiple
                    hidden
                    onChange={(e) => {
                      const files = Array.from(e.target.files);
                      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg'];

                      const filteredFiles = files.filter((file) =>
                          allowedTypes.includes(file.type)
                      )

                      if (filteredFiles.length > 3) filteredFiles.splice(3);
                      formik.setFieldValue('attachments', filteredFiles);
                    }}
                />

                {formik.values.attachments?.length > 0 && (
                    <div className="mt-2">
                      <ul>
                        {formik.values.attachments.map((file, idx) => (
                            <li key={idx} className="text-sm text-muted">
                              {file.name}
                            </li>
                        ))}
                      </ul>
                    </div>
                )}

                {formik.touched.attachments && formik.errors.attachments && (
                    <small className="main_footer_copy text-danger">{formik.errors.attachments}</small>
                )}
              </div>

            </div>

            <div className="d-flex gap-3 mt-3">
              <button type="reset" className="acct_offer_btn2">
                Clear
              </button>
              <button type="submit" className="acct_offer_btn3" disabled={formik.isSubmitting}>
                {formik.isSubmitting ? "Submitting..." : "Submit"}
              </button>
            </div>
          </form>
        </div>
      )}
    </>
  );
}
