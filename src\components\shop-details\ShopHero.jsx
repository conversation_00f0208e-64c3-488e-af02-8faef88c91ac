import {convertToDays} from "../../vbrae-utils/lib/time.js";
import {useFollow} from "../../hooks/shop/useFollow.js";
import Spinner from "../common/Spinner.jsx";
import {getAccessToken} from "../../vbrae-utils/index.js";
import {useContext} from "react";
import {ModalContext} from "../../store/ModalContext.jsx";

export default function ShopHero({coverImage, seller, setOpenMessage, followers}){

    const {OpenModal} = useContext(ModalContext)
    const {followLoading, followRefetch} = useFollow();

    const handleLogin = () => {
        if(getAccessToken()) followRefetch().finally();
        else OpenModal("login")
    }

    return (
        <>
            <div
                className="shop_details_bg position-relative"
                style={{
                    backgroundImage: `url(${coverImage})`,
                }}>
                <div className="shop_total_con">
                    <div className="shop_total_cont d-flex flex-wrap gap-4 justify-content-between align-items-center">
                        <div className="col-12 col-lg-auto d-flex gap-3">
                            <div className="shop_banner_profile position-relative">
                                <img
                                    src={"./assets/images/icons/verify.svg"}
                                    alt=""
                                    className="shop_banner_profile_check position-absolute"
                                />
                                <img
                                    src={seller.avatar || `${window.location.origin}/assets/images/user.png`}
                                    alt=""
                                    className="shop_banner_profile_img"
                                />
                            </div>
                            <div className="col">
                                <div className="d-flex gap-3 align-items-center mb-1">
                            <span
                                className="details_profile_tag position-relative d-flex align-items-center"
                                role="button">
                              <svg
                                  className="position-absolute"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                                />
                              </svg>
                                {seller.sellerStats.averageRating.toFixed(1)}
                            </span>
                                    <span
                                        className="details_profile_tag seller position-relative d-flex align-items-center">
                              <svg
                                  className="position-absolute"
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 256 256">
                                <path
                                    fill="currentColor"
                                    d="M239.75 90.81c0 .11 0 .21-.07.32L217 195a16 16 0 0 1-15.72 13H54.71A16 16 0 0 1 39 195L16.32 91.13c0-.11-.05-.21-.07-.32A16 16 0 0 1 44 77.39l33.67 36.29l35.8-80.29a1 1 0 0 0 0-.1a16 16 0 0 1 29.06 0a1 1 0 0 0 0 .1l35.8 80.29L212 77.39a16 16 0 0 1 27.71 13.42Z"
                                />
                              </svg>
                              {seller.sellerStats.tier}
                            </span>
                                </div>
                                <p className="shop_banner_profile_name">
                                    <span className="text-white">{seller.name}</span>
                                </p>
                                <p className="shop_banner_profile_time">
                                    since {convertToDays(seller.createdAt)}
                                </p>
                            </div>
                        </div>

                        <div className="col col-xl-auto row g-4 g-xl-5">
                            <div className="col-6 col-xl">
                            <p className="shop_banner_head">Orders</p>
                                <p className="shop_banner_num">{seller.sellerStats.totalOrders ?? 0}</p>
                            </div>
                            <div className="col-6 col-xl">
                                <p className="shop_banner_head">Reviews</p>
                                <p className="shop_banner_num">{seller.reviews ?? 0}</p>
                            </div>
                            <div className="col-6 col-xl">
                                <p className="shop_banner_head">Positive Feedback</p>
                                <p className="shop_banner_num d-flex align-items-center">
                                    00%
                                    <span className="game_badge type1 ms-2">
                              Superb!
                            </span>
                                </p>
                            </div>
                            <div className="col-6 col-xl">
                                <p className="shop_banner_head">Followers</p>
                                <div className="shop_banner_num d-flex align-items-center">
                                    {followers.length}
                                    <div className="d-flex ms-4">
                                        {followers.slice(0,3).map(item=> (
                                            <img
                                                key={item._id}
                                                src={item.avatar || `${window.location.origin}/assets/images/user.png`}
                                                alt=""
                                                className="shop_banner_num_img"
                                            />
                                        ))}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="col-12 col-lg-auto d-flex gap-3">
                            <button
                                onClick={() => setOpenMessage(true)}
                                type="button"
                                className="col shop_banner_btn">
                                Contact Seller
                            </button>
                            {followLoading ? <Spinner className="mt-2"/> :
                                <span className="shop_banner_btn_icon d-flex justify-content-center align-items-center" onClick={handleLogin}>
                                  <svg
                                      xmlns="http://www.w3.org/2000/svg"
                                      width="1em"
                                      height="1em"
                                      viewBox="0 0 24 24">
                                    <path
                                        fill={true ? "white" : "#8F97B1"}
                                        d="M13 14.062V22H4a8 8 0 0 1 9-7.938M12 13c-3.315 0-6-2.685-6-6s2.685-6 6-6s6 2.685 6 6s-2.685 6-6 6m5.793 6.914l3.535-3.535l1.415 1.414l-4.95 4.95l-3.536-3.536l1.415-1.414z"
                                    />
                                  </svg>
                            </span>}
                        </div>
                    </div>
                </div>
            </div>
        </>
    )
}