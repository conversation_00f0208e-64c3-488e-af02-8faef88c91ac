import {clearAccessToken} from "../../../vbrae-utils/index.js";
import {Link} from "react-router-dom";
import {useProfile} from "../../../hooks/auth/useProfile.js";
import {useConversations} from "../../../hooks/conversation/useConversations.js";

export default function MobileLoggedIn({role}){

    const {user} = useProfile();
    const {conversations} = useConversations();

    const nameToShow = user?.role === "client" ? "unreadClient" : "unreadSeller";

    const totalUnreadMessages = (conversations ?? []).reduce((acc, cur) => {
        acc += cur[nameToShow];
        return acc;
    }, 0)

    return (
        <div className="header_drop_cont position-absolute">

            {role === "seller" ? (
                <Link to={"/account"}>
                    <p className="header_drop_text d-flex gap-2 align-items-center">
                  <span className="icon">
                    <svg width="1em" height="1em" viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4"
                      />
                    </svg>
                  </span>
                        Account
                    </p>
                </Link>
            ) : (
                <>
                    <Link to={"/account/profile"}>
                        <p className="header_drop_text d-flex gap-2 align-items-center">
                  <span className="icon">
                    <svg width="1em" height="1em" viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4"
                      />
                    </svg>
                  </span>
                            Profile
                        </p>
                    </Link>
                    <Link to={"/account/wishlist"}>
                        <p className="header_drop_text d-flex gap-2 align-items-center">
                  <span className="icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1rem" height="1rem" viewBox="0 0 24 24" fill="currentColor"
                         stroke="currentColor"
                         className="lucide lucide-bookmark-icon lucide-bookmark"><path
                        d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/></svg>
                  </span>
                            Wishlist
                        </p>
                    </Link>
                    <Link to={"/account/messages"}>
                        <p className="header_drop_text d-flex gap-2 align-items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="1rem" height="1rem" viewBox="0 0 24 24"
                                 fill="currentColor" stroke="currentColor" strokeWidth="2" strokeLinecap="round"
                                 strokeLinejoin="round"
                                 className="lucide lucide-message-circle-icon lucide-message-circle">
                                <path d="M7.9 20A9 9 0 1 0 4 16.1L2 22Z"/>
                            </svg>
                            Messages
                            {totalUnreadMessages > 0 && <span
                                className="header_cart_num d-flex justify-content-center align-items-center">{totalUnreadMessages}</span>}
                        </p>
                    </Link>
                </>
            )}

            <Link to={"/account/orders"}>
                <p className="header_drop_text d-flex gap-2 align-items-center">
                          <span className="icon">
                            <svg width="1em" height="1em" viewBox="0 0 24 24"><path
                                fill="currentColor"
                                d="M14.3636 3.76923C14.3636 3.38688 14.6736 3.07692 15.0559 3.07692H15.6818C16.4098 3.07692 17 3.66709 17 4.3951V17C17 18.1046 16.1046 19 15 19H5C3.89543 19 3 18.1046 3 17V4.39511C3 3.66709 3.59017 3.07692 4.31818 3.07692H4.94406C5.32641 3.07692 5.63636 3.38688 5.63636 3.76923C5.63636 4.15158 5.94632 4.46154 6.32867 4.46154H13.6713C14.0537 4.46154 14.3636 4.15158 14.3636 3.76923ZM4.90909 9.30769C4.90909 9.69004 5.21905 10 5.6014 10H14.3986C14.781 10 15.0909 9.69004 15.0909 9.30769C15.0909 8.92534 14.781 8.61539 14.3986 8.61539H5.6014C5.21905 8.61539 4.90909 8.92534 4.90909 9.30769ZM4.90909 14.8462C4.90909 15.2285 5.21905 15.5385 5.6014 15.5385H14.3986C14.781 15.5385 15.0909 15.2285 15.0909 14.8462C15.0909 14.4638 14.781 14.1538 14.3986 14.1538H5.6014C5.21905 14.1538 4.90909 14.4638 4.90909 14.8462ZM8.12937 3.07692C7.55585 3.07692 7.09091 2.61199 7.09091 2.03846C7.09091 1.46494 7.55584 1 8.12937 1H11.8706C12.4442 1 12.9091 1.46494 12.9091 2.03846C12.9091 2.61199 12.4442 3.07692 11.8706 3.07692H8.12937Z"></path></svg>
                          </span>
                    Orders
                </p>
            </Link>
            <Link to={"/account/tickets"}>
                <p className="header_drop_text d-flex gap-2 align-items-center">
                          <span className="icon">
                            <svg width="1em" height="1em" viewBox="0 0 20 20">
                              <g fill="currentColor">
                                <path
                                    d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0 0 16 4H4a2 2 0 0 0-1.997 1.884"></path>
                                <path d="m18 8.118l-8 4l-8-4V14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2z"></path>
                              </g>
                            </svg>
                          </span>
                    Tickets
                </p>
            </Link>
            <Link to={"/account/reviews"}>
                <p className="header_drop_text d-flex gap-2 align-items-center">
                          <span className="icon">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="currentColor"
                                  d="m5.825 21l1.625-7.025L2 9.25l7.2-.625L12 2l2.8 6.625l7.2.625l-5.45 4.725L18.175 21L12 17.275z"></path>
                            </svg>
                          </span>
                    Reviews
                </p>
            </Link>
            <Link to={"/affiliate"}>
                <p className="header_drop_text d-flex gap-2 align-items-center">
                          <span className="icon">
                            <svg
                                width="1em"
                                height="1em"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                xmlns="http://www.w3.org/2000/svg">
                              <path
                                  d="M13.2891 12.9615C13.2891 12.9615 15.632 10.6385 16.4266 9.82056C18.9415 7.36673 17.6882 2.31184 17.6882 2.31184C17.6882 2.31184 12.6501 1.06039 10.1925 3.57148C8.27556 5.43639 7.03038 6.72874 7.03038 6.72874C7.03038 6.72874 3.88465 6.07438 2.00049 7.95565L12.0357 18C13.9198 16.0942 13.2891 12.9615 13.2891 12.9615ZM12.0603 5.59998C12.2124 5.44788 12.3931 5.32722 12.592 5.24489C12.7909 5.16257 13.0041 5.12019 13.2194 5.12019C13.4347 5.12019 13.6479 5.16257 13.8468 5.24489C14.0457 5.32722 14.2264 5.44788 14.3786 5.59998C14.6076 5.82883 14.7634 6.12032 14.8265 6.4376C14.8896 6.75488 14.857 7.08371 14.7329 7.38253C14.6089 7.68134 14.3989 7.93672 14.1295 8.1164C13.8601 8.29607 13.5434 8.39197 13.2194 8.39197C12.8955 8.39197 12.5788 8.29607 12.3094 8.1164C12.04 7.93672 11.83 7.68134 11.7059 7.38253C11.5819 7.08371 11.5493 6.75488 11.6124 6.4376C11.6754 6.12032 11.8313 5.82883 12.0603 5.59998ZM2.81969 17.1821C4.33608 17.1389 5.78777 16.5591 6.91569 15.5462L4.45809 13.0923C2.81969 13.9103 2.81969 17.1821 2.81969 17.1821Z"/>
                            </svg>
                          </span>
                    Affiliate Program
                </p>
            </Link>

            <hr className="header_drop_line my-2"/>

            <Link onClick={() => {
                clearAccessToken();
                window.location.assign("/");
            }}>
                <p className="header_drop_text d-flex gap-2 align-items-center">
                    Log Out
                </p>
            </Link>
        </div>
    )
}