import {useContext, useEffect} from "react";
import {ModalContext} from "../store/ModalContext";
import ResetPasswordForm from "../components/auth/reset-password-form.jsx";
import {useSearchParams} from "react-router-dom";

export default function ResetPasswordModal() {
    const { activeModal, CloseModal, OpenModal } = useContext(ModalContext); //Modal Global State

    const [searchParams] = useSearchParams();
    const token = searchParams.get("token");

    useEffect(() => {
        if(!token) return;

        OpenModal("reset")
    }, [token]);

    if (activeModal !== "reset") return null;

    return (
        <>
            <div className="modal_con d-lg-flex justify-content-center align-items-center">
                <div className="col col-lg-10 col-xl-6 register_con">
                    <div
                        className="col d-none d-lg-block login_img"
                        style={{
                            backgroundImage: "url(./assets/images/login_img.png)",
                        }}></div>
                    <div className="col register_cont position-relative">
            <span
                onClick={() => CloseModal()}
                className="register_close position-absolute">
              <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24">
                <path
                    fill="currentColor"
                    d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"
                />
              </svg>
            </span>
                        <p className="register_header">Welcome back</p>
                        <ResetPasswordForm/>
                    </div>
                </div>
            </div>
        </>
    );
}
