import {postMessageNotification} from "../../api/conversation/postMessageNotification.js";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {useQuery} from "react-query";

export const useMessageNotification = (props) => {

    const { refetch } = useQueryFix({
        query: useQuery({
            queryKey: ['messages-notification', props.message],
            queryFn: () => postMessageNotification(props),
            onError: showError,
            refetchOnWindowFocus: true,
            enabled: false,
        }),
        transform: (data) => data.messages,
    });

    return { refetch };
};