import Pagination from "../../pages/componets/utility/Pagination.jsx";
import {useSellerOffers} from "../../hooks/offers/useSellerOffers.js";
import {generateQuery} from "../../vbrae-utils/lib/misc.js";
import {useEffect, useState} from "react";
import {getUserId} from "../../vbrae-utils/index.js";
import Spinner from "../common/Spinner.jsx";
import useDeleteOffer from "../../hooks/offers/useDeleteOffer.js";
import useUpdateOffer from "../../hooks/offers/useUpdateOffer.js";
import MobileOfferRow from "../offers/MobileOfferRow.jsx";
import OfferRow from "../offers/OfferRow.jsx";
import SetRetailPrice from "../../pages/componets/modals/OfferModal.jsx";

export default function MyOffers({selectedStock}){

    const userId = getUserId();
    const [page, setPage] = useState(1);
    const [offerToDelete, setOfferToDelete] = useState({_id: ""});
    const [offerToUpdate, setOfferToUpdate] = useState({_id: ""});

    const {deleteRefetch} = useDeleteOffer(offerToDelete);
    const {updateRefetch} = useUpdateOffer(offerToUpdate)

    const [isRetailPriceOpen, setIsRetailPriceOpen] = useState({value:false, _id: "", expectedPrice:0, customerPays: 0});

    const {offers, offersLoading} = useSellerOffers({query: generateQuery({
            page,
            userId,
            selectedStock,
            staticString: `active=true&userId=${userId}&limit=12`,
        })});

    useEffect(() => {
        if(!offerToUpdate._id) return;

        updateRefetch().finally(()=> setOfferToUpdate({_id: ""}))
    }, [offerToUpdate]);

    useEffect(() => {
        if(!offerToDelete._id) return;

        deleteRefetch().finally(()=> setOfferToDelete({_id: ""}))
    }, [offerToDelete])

    if(offersLoading) return <Spinner />

    function CloseRetailModal() {
        setIsRetailPriceOpen({value:false, _id: "", expectedPrice:0, customerPays: 0});
    }

    return (
        <>
            {/* Mobile Table */}
            <div className="col d-lg-none mb-4">
                {offers.data.map(item=> <MobileOfferRow key={item._id} {...item} setProductToDelete={setOfferToDelete} setOfferToUpdate={setOfferToUpdate} />)}
            </div>

            {/* Desktop Table */}
            <SetRetailPrice
                isOpen={isRetailPriceOpen}
                onClose={() => CloseRetailModal()}
            />
            <div className="table-responsive d-none d-lg-block">
                <table className="w-100 table table-borderless acct_table">
                    <thead>
                    <tr>
                        <th className="acct_table_head"></th>
                        <th className="acct_table_head">Name</th>
                        <th className="acct_table_head d-flex align-items-center gap-1">
                            Stock / Sold
                            <span className="icon">
                                <svg
                                    width="20"
                                    height="20"
                                    viewBox="0 0 20 20"
                                    fill="currentColor"
                                    xmlns="http://www.w3.org/2000/svg">
                                  <path
                                      d="M3 5V6.66667H7.66667V5H3ZM3 9.16667V10.8333H12.3333V9.16667H3ZM3 13.3333V15H17V13.3333H3Z"/>
                                </svg>
                              </span>
                        </th>
                        <th className="acct_table_head">Sales</th>
                        <th className="acct_table_head">Lowest Price</th>
                        <th className="acct_table_head">Pos</th>
                        <th className="acct_table_head">Achievement</th>
                        <th className="acct_table_head">Sales Booster</th>
                        <th className="acct_table_head">IWTR / Price</th>
                        <th className="acct_table_head"></th>
                    </tr>
                    </thead>
                    <tbody>
                    {offers.data.map(item => <OfferRow
                        {...item}
                        setOfferToUpdate={setOfferToUpdate}
                        setIsRetailPriceOpen={setIsRetailPriceOpen}
                        setProductToDelete={setOfferToDelete}
                        key={item._id} />)}
                    </tbody>
                </table>
            </div>

            {!!offers.pagination.pages && <div className="col d-flex justify-content-center">
                <Pagination
                    totalPages={offers.pagination.pages}
                    currentPage={page}
                    pageClick={(page) => setPage(page)}
                    nextPage={() => setPage(page + 1)}
                    prevPage={() => setPage(page - 1)}/>
            </div>}
        </>
    )
}