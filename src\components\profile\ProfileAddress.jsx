import AddressCard from "../../pages/componets/checkout/AddressCard.jsx";
import {useProfile} from "../../hooks/auth/useProfile.js";
import Spinner from "../common/Spinner.jsx";
import {useEffect, useRef, useState} from "react";
import {showError} from "../../vbrae-utils/index.js";

export default function ProfileAddress({formik}){

    const formRef = useRef(null);
    const [address, setAddress] = useState({
        firstName: "",
        lastName: "",
        details: "",
    });
    const {user, userLoading} = useProfile();

    useEffect(() => {
        if(!user) return;

        const [firstName, lastName] = user.name.split(" ");
        setAddress({
            firstName,
            lastName,
            address: "",
        })
    }, [user]);

    if(!user || userLoading) return <Spinner />;

    const handleRemoveAddress = (_id, index)=>{
        const filteredAddresses = formik.values.addresses.filter((_, i) => i !== index);
        formik.setFieldValue("addresses", filteredAddresses);
    }

    const handleAddress = ()=>{
        if(!address.firstName || !address.details) return showError({message: "Invalid keys"});
        const [street, city, state, country] = address.details.split(",");
        if(!street || !city || !state || !country) return showError({message: "Invalid address keys"});

        const [firstName, lastName] = user.name.split(" ");
        const newAddress = {
            firstName,
            lastName,
            street,
            city,
            state,
            country,
        }
        formik.setFieldValue("addresses", [...formik.values.addresses, newAddress])
    }

        return (
        (
            <div className="col pro_tab_cont">
                <p className="acct_offer_title gray mb-3">Addresses</p>

                <div className="col d-flex flex-wrap gap-3 mb-4">
                    {formik.values.addresses.map((address, index) => (
                        <AddressCard {...address} key={address._id} userName={user.name}
                                     onRemove={(_id) => handleRemoveAddress(_id, index)}/>
                    ))}
                    <div className="col">
                        <div className="col h-100 cart_add_item  new d-flex justify-content-center align-items-center">
                            <p className="cart_add_new d-flex gap-2 align-items-center"
                               onClick={() => formRef.current?.focus()}>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="20"
                                    height="20"
                                    viewBox="0 0 24 24">
                                    <path
                                        fill="currentColor"
                                        d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10s10-4.486 10-10S17.514 2 12 2m5 11h-4v4h-2v-4H7v-2h4V7h2v4h4z"></path>
                                </svg>
                                Add new address
                            </p>
                        </div>
                    </div>
                </div>
                <div className="d-flex flex-wrap">
                    <div className="col">
                        <div className="d-md-flex align-items-end gap-3">
                            <div className="col mb-3">
                                <label className="acct_offer_label mb-2">
                                    First name*
                                </label>
                                <div className="acct_box active">
                                    <input
                                        type="text"
                                        disabled={true}
                                        name="firstName"
                                        className="input_box "
                                        value={address.firstName}
                                        onChange={e => setAddress({...address, firstName: e.target.value})}
                                    />
                                </div>
                            </div>
                            <div className="col mb-3">
                                <label className="acct_offer_label mb-2">
                                    Last name*
                                </label>
                                <div className="acct_box active">
                                    <input
                                        type="text"
                                        disabled={true}
                                        name="lastName"
                                        className="input_box "
                                        value={address.lastName}
                                        onChange={e => setAddress({...address, lastName: e.target.value})}
                                    />
                                </div>
                            </div>
                        </div>
                        <div className="d-mb-flex align-items-end gap-3">
                            <div className="col mb-3">
                                <label className="acct_offer_label mb-2">
                                    Street, City, State, Country (comma seperated)*
                                </label>
                                <div className="acct_box active">
                                    <input
                                        ref={formRef}
                                        type="text"
                                        name="amount"
                                        className="input_box "
                                        value={address.details}
                                        onChange={e => setAddress(prev => ({...prev, details: e.target.value}))}
                                    />
                                </div>
                            </div>
                        </div>
                        <p className="acct_offer_hint d-flex gap-2 align-items-center mb-4">
                        <span className="icon2">
                          <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 24 24">
                            <path
                                fill="currentColor"
                                fillRule="evenodd"
                                d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10M12 6.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V7a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2"
                                clipRule="evenodd"></path>
                          </svg>
                        </span>
                            If you want to receive an invoice, please fill out the
                            data below
                        </p>
                        <div className="col ">
                            <p className="acct_offer_title small d-flex gap-3 align-items-center mb-4">
                          <span className="icon">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="none"
                                  stroke="currentColor"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="m5 15l7-7l7 7"></path>
                            </svg>
                          </span>
                                Hide additional fields
                            </p>

                            <div className="col">
                                <div className="d-md-flex gap-3">
                                    <div className="col mb-3">
                                        <label className="acct_offer_label mb-2">
                                            Company
                                        </label>
                                        <div className="acct_box">
                                            <input
                                                type="text"
                                                id="company"
                                                className="input_box "
                                                {...formik.getFieldProps("company")}
                                            />
                                        </div>
                                    </div>
                                    <div className="col mb-3">
                                        <label className="acct_offer_label mb-2">
                                            Tax ID
                                        </label>
                                        <div className="acct_box">
                                            <input
                                                type="text"
                                                name="companyTaxId"
                                                className="input_box "
                                                {...formik.getFieldProps("companyTaxId")}
                                            />
                                            {formik.touched.companyTaxId &&
                                                <small className="main_footer_copy text-danger">{formik.errors.companyTaxId}</small>}
                                        </div>
                                    </div>
                                </div>
                                <div className="d-md-flex gap-3">
                                    <div className="col mb-3">
                                        <label className="acct_offer_label mb-2">
                                            Telephone
                                        </label>
                                        <div className="acct_box">
                                            <input
                                                type="text"
                                                name="telephone"
                                                className="input_box "
                                                {...formik.getFieldProps("telephone")}
                                            />
                                            {formik.touched.telephone &&
                                                <small className="main_footer_copy text-danger">{formik.errors.telephone}</small>}
                                        </div>
                                    </div>
                                    <div className="col mb-3">
                                        <label className="acct_offer_label mb-2">
                                            Fax
                                        </label>
                                        <div className="acct_box">
                                            <input
                                                type="text"
                                                name="fax"
                                                className="input_box "
                                                {...formik.getFieldProps("fax")}
                                            />
                                            {formik.touched.fax &&
                                                <small className="main_footer_copy text-danger">{formik.errors.fax}</small>}
                                        </div>
                                    </div>
                                </div>

                                <div className="d-flex gap-3">
                                    <div className="col-12 col-md d-flex gap-2 mb-3">
                                        <button
                                            type="button"
                                            className="col acct_offer_btn2">
                                            Cancel
                                        </button>
                                        <button
                                            type="button"
                                            onClick={handleAddress}
                                            className="col acct_offer_btn1">
                                            Add
                                        </button>
                                    </div>
                                    <div className="col"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="col-12 col-xl-4"></div>
                </div>
            </div>
        )
    )
}