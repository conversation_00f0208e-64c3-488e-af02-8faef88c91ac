import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {postReply} from "../../api/rating/postReply.js";

const schema = Yup.object().shape({
    sellerResponse: Yup.string().required("Response is required")
});

export default function useReplyForm({onClose, data}) {

    const queryClient = useQueryClient();

    const initialValues = {
        sellerResponse: '',
    }

    const { mutateAsync } = useMutation(postReply, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: schema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync({...values, ...data});
            setSubmitting(false);
            resetForm();
            if (response) {
                queryClient.invalidateQueries(['reviews']).finally()
                showSuccess("Reply added Successfully");
                onClose();
            }
        },
    });

    return { formik };
}
