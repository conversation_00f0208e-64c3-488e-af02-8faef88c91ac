import {Swiper, SwiperSlide} from "swiper/react";
import {Pagination} from "swiper/modules";
import GameItem from "../GameItem.jsx";
import {useTopOffers} from "../../../hooks/offers/useTopOffers.js";
import {generateQuery} from "../../../vbrae-utils/lib/misc.js";
import {getUserId} from "../../../vbrae-utils/index.js";
import Spinner from "../../../components/common/Spinner.jsx";

export default function CartOffers(){

    const userId = getUserId();
    const {topOffers, topOfferLoading} = useTopOffers({query: generateQuery({
            limit: 5,
            page: 1,
            staticString: `${userId ? `userId=${userId}` : ''}`,
        })});

    if(topOfferLoading || !topOffers) {
        return <Spinner />
    }

    return (
        <div className="">
            <Swiper
                modules={[Pagination]}
                spaceBetween={10}
                slidesPerView={"auto"}
                onSwiper={(swiper) => {
                    swiper.wrapperEl.classList.add("game_slider_con");
                }}
                pagination={{
                    el: ".custom_pagination",
                    clickable: true,
                    renderBullet: function (index, className) {
                        return `<span class="${className} custom_bullet"></span>`;
                    },
                }}>
                {topOffers.map((offer, index) => (
                    <SwiperSlide key={index} style={{width: "auto"}}>
                        <GameItem {...offer}/>
                    </SwiperSlide>
                ))}

                <div className="custom_pagination d-flex justify-content-center mt-4"></div>
            </Swiper>
        </div>
    )
}