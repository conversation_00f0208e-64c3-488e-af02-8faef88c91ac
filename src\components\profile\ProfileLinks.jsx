import {useState} from "react";

export default function ProfileLinks({formik}){

    const [formState, setFormState] = useState({
        title:"", url:""
    })

    const handleSubmit = () => {

        if(!formState.title || !formState.url) return;

        formik.setFieldValue("links", [...formik.values.links, formState])
        setFormState({ title:"", url:"" })
    }

    return (
        <div className="col pro_tab_cont">
            <p className="acct_offer_title gray mb-3">Links</p>

            <div className="col">
                <div className="d-md-flex align-items-end gap-3 mb-2">
                    <div className="col-12 col-md mb-3">
                        <label className="acct_offer_label mb-2">
                            Link Title
                        </label>
                        <div className="acct_box filled">
                            <input
                                type="text"
                                name="title"
                                className="input_box "
                                placeholder="Enter Link Title"
                                value={formState.title}
                                onChange={e=> setFormState(prevState => ({...prevState, title: e.target.value}))}
                            />
                        </div>
                    </div>
                    <div className="col-12 col-md mb-3">
                        <label className="acct_offer_label mb-2">URL</label>
                        <div className="acct_box filled">
                            <input
                                type="text"
                                name="url"
                                className="input_box "
                                placeholder="Url"
                                value={formState.url}
                                onChange={e=> setFormState(prevState => ({...prevState, url: e.target.value}))}
                            />
                        </div>
                    </div>
                    <button
                        type="button"
                        onClick={handleSubmit}
                        className="col-12 col-md-3 acct_offer_btn1 mb-3">
                        Add Link
                    </button>
                </div>

                <div className="col">
                    {formik.values.links.map((item, index)=> (
                        <div
                            className="pro_link_cont d-md-flex justify-content-between align-items-center position-relative mb-2">
                            <div className="form-check form-switch d-flex gap-3 align-items-center mb-3 mb-md-0">
                                <input
                                    className="form-check-input"
                                    type="checkbox"
                                    role="switch"
                                    id={item.title}
                                    checked
                                />
                                <label
                                    className="acct_offer_title small"
                                    htmlFor={item.title}>
                                    {item.title}
                                </label>
                            </div>
                            <div className="d-flex gap-3 align-items-center" key={item._id}>
                                <p className="acct_offer_title light dark small">
                                    {item.url}
                                </p>
                                <span className="pro_link_icon"
                                      onClick={() => {
                                          const updatedLinks = formik.values.links.filter((_, idx) => idx !== index);
                                          formik.setFieldValue("links", updatedLinks);
                                      }}>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 32 32">
                                  <path
                                      fill="currentColor"
                                      d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"></path>
                                </svg>
                              </span>
                            </div>
                        </div>
                    ))}
            </div>
        </div>
</div>
)
}