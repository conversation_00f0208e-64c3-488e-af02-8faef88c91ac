import {useTimer} from "react-timer-hook";
import {Link} from "react-router-dom";
import {getIcon} from "../../vbrae-utils/lib/getIcons.jsx";

export default function BannerTwo({endTime, images, link, category, discountPercent, existingPrice, couponCode}){

    const [p1, p2] = existingPrice.split(".");

    const { seconds, minutes, hours, days } = useTimer({
        expiryTimestamp: endTime,
        onExpire: () => console.warn("onExpire called"),
    });

    return (
        <Link to={link} className="col banner_sm1_con" target="_blank"
              style={{backgroundImage: `url(${images.desktop})`}}>
            <div className="banner_sm1_col d-block">
                <div className="banner_sm1_cont d-flex flex-column">
                    <div className="d-flex justify-content-between align-items-start ">
                        <p className="banner_sm_time">
                            ends in: {days}d | {hours}h | {minutes}m | {seconds}s
                        </p>
                        <span className="banner_sm_icon">
                            {getIcon(category)}
                          </span>
                    </div>

                    <div className="banner_sm_coupon d-flex align-items-center mb-2">
                        <span> {discountPercent}%</span>
                        <p className="ms-1 mb-0">
                            <span>off </span>
                            with <br/> code
                        </p>
                    </div>

                    <div className="banner_sm_coupon_code d-flex align-items-center mb-2">
                        <p
                            className="mb-0"
                            style={{backgroundImage: `url(${window.location.origin}/assets/images/vouche_sm.svg)`}}>
                            {couponCode}
                        </p>
                    </div>

                    <p className="banner_sm_price mt-auto mb-0">
                        ${p1}{p2 && <span>.{p2}</span>}
                    </p>
                </div>
            </div>
        </Link>
    )
}