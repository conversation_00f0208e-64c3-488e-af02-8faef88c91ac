import {useBlogCategories} from "../../hooks/blogs/useBlogCategories.js";
import Spinner from "../common/Spinner.jsx";

export default function MobileFilters({setActiveFilter}){

    const {categories, categoriesLoading} = useBlogCategories();

    if(!categories || categoriesLoading) return <Spinner />

    return (
        <select className="col-12 col-md-4 cate_filter_sort_sel d-lg-none my-3" onChange={(e) => setActiveFilter(prevState => ({...prevState, category: e.target.value}))}>
            <option value="">Category: All</option>
            {categories.map((filter) => (
                <option value={filter._id} key={filter._id}>Category: {filter.categoryName}</option>
            ))}
        </select>
    )
}