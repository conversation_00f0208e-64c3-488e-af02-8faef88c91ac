/* eslint-disable react/prop-types */
import {useRef, useState} from "react";
import {Link} from "react-router-dom";
import {useKycUrl} from "../../hooks/auth/useKycUrl.js";
import Spinner from "../common/Spinner.jsx";
import {useProfile} from "../../hooks/auth/useProfile.js";

export default function ProfileDetails({formik, avatar, coverImage}) {

    const {kycUrl, kycLoading} = useKycUrl();
    const {user} = useProfile();

    const fileInputRef = useRef(null);
    const coverUploadRef = useRef(null);

    const [coverPreview, setCoverPreview] = useState(coverImage);
    const [avatarPreview, setAvatarPreview] = useState(avatar);

    const handleFileChange = (event, type) => {
        const file = event.target.files?.[0];
        if (file) {
            const blobUrl = URL.createObjectURL(file);
            if(type === 'avatar') {
                setAvatarPreview(blobUrl);
                formik.setFieldValue("avatar", file)
            }
            else {
                setCoverPreview(blobUrl);
                formik.setFieldValue("coverImage", file)
            }
        }
    };

    return (
        <form className="col pro_tab_cont">
            <p className="acct_offer_title gray mb-4">Account Details</p>

            <div className="col position-relative mb-4">
                <div className="pro_up_img_cont text-center ">
                    <p className="acct_offer_label mb-2">AVATAR Image</p>
                    <img
                        src={avatarPreview ? avatarPreview : `${window.location.origin}/assets/images/user.png`}
                        alt=""
                        className="pro_up_img mb-2"
                    />
                    <div className="d-flex justify-content-center">
                        <button type="button" className="acct_offer_btn2" onClick={()=> fileInputRef.current?.click()}>
                            Change
                        </button>
                        <input type="file" ref={fileInputRef} className="d-none" onChange={e=>handleFileChange(e,'avatar')}/>
                    </div>
                </div>
                <div
                    className="col pro_up d-flex justify-content-center"
                    style={{
                        backgroundImage: `url(${coverPreview ? coverPreview : `${window.location.origin}/assets/images/pro_upload_bg.png`})`
                    }}>
                <div className="col col-lg-6 col-xl-4 text-center">
                        <p className="acct_offer_label mb-2">
                            Cover shop Image (1920x400){" "}
                        </p>
                        <p className="acct_offer_hint d-flex gap-1 justify-content-center align-items-start">
                          <span className="icon2">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="currentColor"
                                  fillRule="evenodd"
                                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10M12 6.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V7a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2"
                                  clipRule="evenodd"></path>
                            </svg>
                          </span>
                            This image will only be displayed on computers and
                            laptops
                        </p>
                        <img
                            src={"../assets/images/fade_logo.png"}
                            className="my-4"
                            height={"60px"}
                        />
                        <div className="d-flex justify-content-center">
                            <button type="button" className="acct_offer_btn2" onClick={()=> coverUploadRef.current?.click()}>
                                Upload
                            </button>
                            <input type="file" ref={coverUploadRef} className="d-none" onChange={e=>handleFileChange(e, "cover")}/>
                        </div>
                    </div>
                </div>
            </div>

            <div className="col d-flex flex-wrap gap-3">
                <div className="col">
                    <div className="d-md-flex gap-3">
                        <div className="col mb-3">
                            <label className="acct_offer_label green mb-2">
                                Email Address (required)
                            </label>
                            <div className="acct_box filled">
                                <input
                                    type="text"
                                    name="email"
                                    className="input_box "
                                    {...formik.getFieldProps("email")}
                                />
                                {formik.touched.email &&
                                    <small className="main_footer_copy text-danger">{formik.errors.email}</small>}
                            </div>
                        </div>
                        <div className="col mb-3">
                            <label className="acct_offer_label green mb-2">
                                Username (required)
                            </label>
                            <div className="acct_box filled">
                                <input
                                    type="text"
                                    className="input_box"
                                    name="userName"
                                    disabled={true}
                                    {...formik.getFieldProps("userName")}
                                />
                            </div>
                        </div>
                    </div>
                    <div className="d-md-flex gap-3">
                        <div className="col mb-3">
                            <label className="acct_offer_label green mb-2">
                                First Name (required)
                            </label>
                            <div className="acct_box filled">
                                <input
                                    type="text"
                                    name="firstName"
                                    className="input_box "
                                    {...formik.getFieldProps("firstName")}
                                />
                                {formik.touched.firstName &&
                                    <small className="main_footer_copy text-danger">{formik.errors.firstName}</small>}
                            </div>
                        </div>
                        <div className="col mb-3">
                            <label className="acct_offer_label green mb-2">
                                Last Name (required)
                            </label>
                            <div className="acct_box filled">
                                <input
                                    type="text"
                                    name="lastName"
                                    className="input_box"
                                    {...formik.getFieldProps("lastName")}
                                />
                                {formik.touched.lastName &&
                                    <small className="main_footer_copy text-danger">{formik.errors.lastName}</small>}
                            </div>
                        </div>
                    </div>
                    <div className="d-md-flex gap-3">
                        <div className="col mb-3">
                            <label className="acct_offer_label green mb-2">
                                Phone Number (required)
                            </label>
                            <div className="acct_box">
                                <input
                                    type="text"
                                    name="phoneNumber"
                                    className="input_box "
                                    {...formik.getFieldProps("phoneNumber")}
                                />
                                {formik.touched.phoneNumber &&
                                    <small className="main_footer_copy text-danger">{formik.errors.phoneNumber}</small>}
                            </div>
                        </div>
                        <div className="col mb-3">
                            <label className="acct_offer_label green mb-2">
                                Country (required)
                            </label>
                            <div className="acct_box filled">
                                <input
                                    type="text"
                                    name="country"
                                    className="input_box"
                                    {...formik.getFieldProps("country")}
                                />
                                {formik.touched.country &&
                                    <small className="main_footer_copy text-danger">{formik.errors.country}</small>}
                            </div>
                        </div>
                    </div>

                    <div className="col">
                        <div className="col d-flex align-items-center gap-2 mb-3">
                            <input
                                className="acct_offer_check"
                                type="checkbox"
                                name="sendEmailOnNewOrder"
                                id="sendEmailOnNewOrder"
                                checked={formik.values.sendEmailOnNewOrder}
                                onChange={e => formik.setFieldValue("sendEmailOnNewOrder", e.target.checked)}
                            />
                            <label
                                className="acct_offer_text active"
                                htmlFor="defaultCheck2">
                                Send me an email when someone send me a message
                            </label>
                        </div>
                        <div className="col d-flex align-items-center gap-2 mb-3">
                            <input
                                className="acct_offer_check"
                                type="checkbox"
                                name="showLocation"
                                id="showLocation"
                                checked={formik.values.showLocation}
                                onChange={e => formik.setFieldValue("showLocation", e.target.checked)}
                            />
                            <label
                                className="acct_offer_text"
                                htmlFor="showLocation">
                                Show my location
                            </label>
                        </div>
                    </div>
                </div>
                <div className="col-12 col-xl-4">
                    <div className="col pro_info_cont mb-2">
                        <p className="pro_info_head green d-flex gap-2 align-items-center mb-3">
                          <span className="icon">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="currentColor"
                                  fillRule="evenodd"
                                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10M12 6.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V7a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2"
                                  clipRule="evenodd"></path>
                            </svg>
                          </span>
                            Information required
                        </p>
                        <p className="pro_info_text">
                            In order to sell your products, you must be a verified
                            member. Verification is a one-time process. This
                            verification process is necessary because of spammers
                            and fraud.
                        </p>
                    </div>
                    {user?.isKycVerified !== "approved" && <div className="col pro_info_cont mb-2">
                        <p className="pro_info_head green d-flex gap-2 align-items-center mb-3">
                          <span className="icon">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="currentColor"
                                  fillRule="evenodd"
                                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10M12 6.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V7a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2"
                                  clipRule="evenodd"></path>
                            </svg>
                          </span>
                            KYC Verification
                        </p>
                        <p className="pro_info_text mb-4">
                            We work together with Kycaid. Verify your ID within 15
                            seconds
                        </p>
                        {kycLoading ? <Spinner /> : <Link to={kycUrl} target="_blank" className="acct_offer_btn3 text-white">
                            Continue
                        </Link>}
                    </div>}
                </div>
            </div>
        </form>
    )
}