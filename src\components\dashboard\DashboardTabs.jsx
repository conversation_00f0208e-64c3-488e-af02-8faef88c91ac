import {Link} from "react-router-dom";
import MyOffers from "./MyOffers.jsx";
import MySales from "./MySales.jsx";
import {useTabs} from "../../services/CustomTabs.jsx";

export default function DashboardTabs(){

    const { activeTab, ChangeTab } = useTabs(1);

    return (
        <div className="col ">
            <div className="col d-flex gap-3 justify-content-between align-items-center mb-4 mb-lg-3">
                <div className="col d-flex overflow-auto hide_scroll gap-2">
                    <p
                        className={
                            "dash_tab_link " + (activeTab == 1 ? "active" : "")
                        }
                        onClick={() => ChangeTab(1)}>
                        My Offers
                    </p>
                    <p
                        className={
                            "dash_tab_link " + (activeTab == 2 ? "active" : "")
                        }
                        onClick={() => ChangeTab(2)}>
                        OUT OF STOCK
                    </p>
                    <p
                        className={
                            "dash_tab_link " + (activeTab == 3 ? "active" : "")
                        }
                        onClick={() => ChangeTab(3)}>
                        SALES
                    </p>
                </div>
                <Link
                    className="d-none d-lg-block"
                    to={activeTab == 1 ? "/account/offers" : activeTab == 2 ? "/account/offers" : "/account/sales"}
                >
                    <p className="dash_all">View all</p>
                </Link>

            </div>

            {/* My Offers */}
            {activeTab == 1 && <MyOffers/>}
            {activeTab == 2 && <MyOffers selectedStock="stock[lte]=0"/>}
            {activeTab == 3 && <MySales/>}
        </div>
    )
}