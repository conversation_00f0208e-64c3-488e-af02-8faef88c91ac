import {getRequest} from "../../vbrae-utils/index.js";
import {getAccessToken} from "../../vbrae-utils/index.js";

export async function getTickets(query) {
    // Use the new my-tickets endpoint with proper query parameters
    const url = query ? `tickets/my-tickets?${query}` : 'tickets/my-tickets?page=1&limit=10&status=open';

    // Check authentication
    const token = getAccessToken();
    console.log('🎫 Authentication token exists:', !!token);
    console.log('🎫 Token preview:', token ? `${token.substring(0, 20)}...` : 'No token');

    console.log('🎫 Fetching tickets with URL:', url);
    console.log('🎫 Query parameters:', query);

    const response = await getRequest({
        url: url,
        useAuth: true,
    });

    console.log('🎫 Tickets API Response:', response);
    console.log('🎫 Tickets data array:', response?.data);
    console.log('🎫 Number of tickets:', response?.data?.length || 0);
    console.log('🎫 Response status:', response?.status);
    console.log('🎫 Response pagination:', response?.pagination);

    return response;
}
