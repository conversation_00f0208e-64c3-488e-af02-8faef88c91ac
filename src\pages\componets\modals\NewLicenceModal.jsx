import {useState} from "react";
import {connectListing} from "../../../vbrae-utils/lib/misc.js";
import {useSearchParams} from "react-router-dom";

export function NewLicenseModal({setOpen, onSubmit, keysState}) {
    const [searchParams] = useSearchParams();
    const idx = parseInt(searchParams.get("idx"));
    const [formState, setFormState] = useState(keysState[idx].keys.join('\n'));

    const isInstant = keysState[idx].instantDelivery;

    return (
        <>
            <div className="modal_con d-lg-flex justify-content-center align-items-start pt-5 inset-0">
                <div className="col col-lg-10 col-xl-5">
                    <form onSubmit={(e)=> {
                        e.preventDefault();
                        setOpen(false);
                        onSubmit(connectListing(formState),idx);
                    }} className="col modal_cont d-flex flex-column position-relative mb-3">
                      <span
                          className="dash_level_close position-absolute"
                          role="button"
                          onClick={() => setOpen(false)}>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <path
                              fill="currentColor"
                              d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"
                          />
                        </svg>
                      </span>

                        <p className="dash_level_title mb-4">{isInstant ? "Instant" : "Add"} Key</p>

                        <div className="col">
                            <label
                                htmlFor="comment"
                                className="auth_label text-uppercase mb-2">
                                1 line = 1 CD Key / Link (separated by ENTER):
                            </label>
                            <div className="col acct_box">
                              <textarea
                                  name=""
                                  className="input_box"
                                  value={formState}
                                  required={true}
                                  onChange={e => setFormState(e.target.value)}
                                  rows={3}/>
                            </div>
                            <label className="auth_label text-uppercase mb-2">
                                {connectListing(formState).length} Keys added
                            </label>
                        </div>

                        {!isInstant &&
                            <div className="d-flex gap-3 mt-5">
                                <button disabled={!formState} className="acct_offer_btn3">
                                Save
                                </button>
                            </div>
                        }
                    </form>
                </div>
            </div>
        </>
    );
}