import {useQuery, useQueryClient} from "react-query";
import {showError, showSuccess, useQueryFix} from "../../vbrae-utils/index.js";
import {patchOrder} from "../../api/orders/patchOrder.js";
import {useParams} from "react-router-dom";

export const usePatchOrder = (props) => {

    const {id} = useParams();
    const queryClient = useQueryClient();

    const { refetch: orderPatch, loading: patchLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['orders-patch', id],
            queryFn: () => patchOrder({...props, id}),
            onError: showError,
            onSuccess: ()=> queryClient.invalidateQueries("get-sales").finally(()=> showSuccess("Order Updated successfully")),
            enabled: false,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data
    });

    return { orderPatch, patchLoading };
};