const key = 'vb-token';
const userKey = 'vb-user-id'
const salesKey = 'vb-sales-token'
const paymentInfo = 'vb-payment'

export function setAccessToken(token, id, salesToken) {
  localStorage.setItem(key, token);
  localStorage.setItem(userKey, id);
  localStorage.setItem(salesKey, salesToken);
}

export function getAccessToken(){
  return localStorage.getItem(key)
}

export function getSalesToken(){
  return localStorage.getItem(salesKey)
}

export function clearAccessToken() {
  localStorage.removeItem(key);
  localStorage.removeItem(userKey);
  localStorage.removeItem(salesKey);
}

export function getUserId(){
  return localStorage.getItem(userKey)
}

export function setPaymentInfo(value){
  return localStorage.setItem(paymentInfo, value)
}

export function getPaymentInfo(){
  return localStorage.getItem(paymentInfo)
}

