import {Link} from "react-router-dom";
import {useTimer} from "react-timer-hook";
import {getIcon} from "../../vbrae-utils/lib/getIcons.jsx";

export default function BannerFour({link, images, endTime, title, category, discountPercent, existingPrice}) {

    const [p1, p2] = existingPrice.split(".");

    const { seconds, minutes, hours, days } = useTimer({
        expiryTimestamp: endTime,
        onExpire: () => console.warn("onExpire called"),
    });

    return (
        <Link to={link} target="_blank" className="col main_offer_item mb-3 d-block"
              style={{backgroundImage: `url(${images.desktop})`}}>
            <div className="main_offer_details d-flex flex-column justify-content-end">
                <div className="main_offer_timing d-flex align-items-center mb-3">
                    <p className="mb-0 me-2">ends in: {days}d | {hours}h | {minutes}m | {seconds}s</p>
                    {getIcon(category)}
                </div>
                <div className="col-9 mb-3">
                    <p className="main_offer_text">
                        {title}
                    </p>
                </div>

                <div className="d-flex justify-content-between align-items-end">
                    <div className="d-flex align-items-center">
                        <div className="main_offer_coupon d-flex align-items-center mb-0">
                            <span> {discountPercent}%</span>
                            <p className="ms-1 mb-0">
                                <span>off </span>
                                with <br/> code
                            </p>
                        </div>
                        <div className="main_offer_coupon_code d-flex align-items-center">
                            <p
                                className="mb-0"
                                style={{
                                    backgroundImage:
                                        "url(./assets/images/vouche_sm.svg)",
                                }}>
                                SUM10VER
                            </p>
                        </div>
                    </div>

                    <p className="main_offer_price mb-0">
                        $27.<span>75</span>
                    </p>
                </div>
            </div>
        </Link>
    )
}