import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getSellerOffers} from "../../api/offers/getSellerOffers.js";

export const useTopOffers = ({query}) => {
    const { data: topOffers, loading: topOfferLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['game-item','top-offers', query],
            queryFn: () => getSellerOffers({query}),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { topOffers, topOfferLoading};
};