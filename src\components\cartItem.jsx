import {Fragment, useEffect, useState} from "react";
import {usePatchCart} from "../hooks/cart/usePatchCart.js";
import {calculateSavingsPercentage} from "../vbrae-utils/lib/misc.js";
import {useDeleteCart} from "../hooks/cart/useDeleteCart.js";
import Spinner from "./common/Spinner.jsx";
import {usePostWishlist} from "../hooks/wishlist/usePostWishlist.js";

export default function CartItem({offerName, region, customerPays, sellerName, category, numberOfItems, actualPrice, savings, deliveryTime, offer, coverImage, cartId}){

    const [quantityState, setQuantityState] = useState(numberOfItems);

    const {patchLoading, patchRefetch} = usePatchCart({offerId: offer, quantity: quantityState, cartId});
    const {deleteLoading, deleteRefetch} = useDeleteCart({offerId: offer, cartId});
    const {offerRefetch, isFetching} = usePostWishlist({offerId:offer});

    const splitPrice = (customerPays * quantityState).toString().split(".");

    useEffect(() => {
        if(quantityState === numberOfItems) return;
        patchRefetch().finally()
    }, [quantityState]);

    const salePrice = calculateSavingsPercentage(actualPrice, customerPays);

    return (
        <Fragment>
            <div className="col d-flex flex-wrap gap-3 mb-4">
                <img
                    src={coverImage}
                    alt=""
                    className="d-none d-md-block cart_item_img"
                />
                <div className="col d-flex flex-column">
                    <div className="col d-flex gap-3 align-items-start mb-3 mb-md-0">
                        <img
                            src={coverImage}
                            alt=""
                            className="d-md-none cart_item_img"
                        />
                        <div className="col">
                            <p className="cart_item_name">
                                {offerName} {region}
                            </p>
                            <p className="cart_item_price">
                                ${splitPrice[0]}<span className={splitPrice[1] ? '' : 'd-none'}>.{splitPrice[1]}</span>
                            </p>
                        </div>
                    </div>
                    <div className="d-flex flex-wrap gap-3 mt-auto">
                        <p className="cart_item_dea">
                            Sold by: <span>{sellerName}</span>
                        </p>
                        <p className="cart_item_dea">
                            Delivery: <span>{deliveryTime} {deliveryTime !== 'Instant' && 'H'}</span>
                        </p>
                        <p className="cart_item_dea">
                            Type: <span>Digital key</span>
                        </p>
                        <p className="cart_item_dea">
                            Region: <span>{region}</span>
                        </p>
                        <p className="cart_item_dea">
                            Platform: <span>{category}</span>
                        </p>
                    </div>
                </div>
                <div
                    className="col-12 col-md-auto d-flex flex-md-column gap-4 justify-content-between align-items-center align-items-md-end">
                    <div className="col-auto text-md-end">
                        <div className={`d-flex mb-2 align-items-center ${salePrice < 0 ? '' : 'justify-content-end'}`}>
                            {salePrice < 0 && <span
                                className="cart_item_dis">{salePrice}%</span>}
                            <span className="cart_item_price_sm">${actualPrice * quantityState}</span>
                        </div>
                        <p className="cart_item_price text-white mt-auto mb-2">
                            ${customerPays * quantityState}
                        </p>
                        <p className="cart_item_save mb-0">
                            You save <span>${savings * quantityState}</span>
                        </p>
                    </div>

                    <div
                        className="col col-md-auto d-flex justify-content-end align-items-center gap-3 mt-md-auto">
                        {deleteLoading ? <Spinner /> :
                            <span className="cart_item_icon d-flex justify-content-center align-items-center"
                                  onClick={() => deleteRefetch()}>
                              <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"
                                />
                              </svg>
                            </span>}
                        {isFetching ? <Spinner className="w-4 h-4" /> : <span className="cart_item_icon d-flex justify-content-center align-items-center" onClick={() => offerRefetch()}>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                                />
                              </svg>
                        </span>}
                        <div
                            className="col cart_item_count d-flex gap-3 justify-content-between align-items-center">
                              <span onClick={() => quantityState > 1 && setQuantityState(quantityState - 1)}>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 24 24">
                                  <path
                                      fill="currentColor"
                                      d="M19 12.998H5v-2h14z"
                                  />
                                </svg>
                              </span>
                            {patchLoading ? <Spinner /> : quantityState}
                            <span onClick={() => setQuantityState(quantityState + 1)}>
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 24 24">
                                  <path
                                      fill="currentColor"
                                      d="M19 12.998h-6v6h-2v-6H5v-2h6v-6h2v6h6z"
                                  />
                                </svg>
                              </span>
                        </div>
                    </div>
                </div>
            </div>
            {region !== 'Global' && <div className="col d-flex align-items-center gap-2">
                            <span className="cart_item_tip_icon">
                              <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    fillRule="evenodd"
                                    d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10M12 6.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V7a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2"
                                    clipRule="evenodd"
                                />
                              </svg>
                            </span>
                <p className="cart_item_tip_text">
                    This product is region / country restricted to: OTHER.
                    To see the list of supported countries, click here.
                </p>
            </div>}
            <hr className="cart_item_line mb-4"/>
        </Fragment>
    )
}