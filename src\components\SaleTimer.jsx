import {useTimer} from "react-timer-hook";

export default function SaleTimer({ expiryTimestamp, isSmall }) {
    const time = new Date(expiryTimestamp);

    const { seconds, minutes, hours, days } = useTimer({
        expiryTimestamp: time,
        onExpire: () => console.warn("onExpire called"),
    });

    return (
        <>
            <p className={`mb-0 me-2 ${isSmall ? 'banner_sm_time' : ''}`}>
                ends in: {days}d | {hours}h | {minutes}m | {seconds}s
            </p>
        </>
    );
}
