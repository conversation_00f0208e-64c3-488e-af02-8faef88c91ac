import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getCategoryDetails} from "../../api/categories/getCategoryDetails.js";

export const useCategoryDetails = ({_id}) => {
    const { data: category, loading: categoryLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['category-details', _id],
            queryFn: () => getCategoryDetails({_id}),
            onError: showError,
            staleTime: 10000,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { category, categoryLoading};
};