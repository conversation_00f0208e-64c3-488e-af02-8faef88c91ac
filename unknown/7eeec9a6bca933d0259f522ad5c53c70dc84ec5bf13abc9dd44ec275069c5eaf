import {getBlogFilters} from "../../api/blog/getBlogFilters.js";

import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";

export const useBlogFilters = () => {
    const { data: filters, loading } = useQueryFix({
        query: useQuery({
            queryKey: ['blog-filters'],
            queryFn: () => getBlogFilters(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.filters,
    });

    return { filters, loading};
};