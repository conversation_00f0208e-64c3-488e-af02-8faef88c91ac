import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getMyShops} from "../../api/shop/getMyShops.js";

export const useMyShops = ({user}) => {

    const { data: shop, loading: shopLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['shops-me'],
            queryFn: () => getMyShops(),
            onError: showError,
            enabled: !!user && user.role === "seller",
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { shop, shopLoading};
};