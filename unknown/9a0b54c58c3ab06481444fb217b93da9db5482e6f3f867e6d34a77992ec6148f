import {useQuery} from "react-query";
import {getAccessToken, useQueryFix} from "../../vbrae-utils/index.js";
import {getProfile} from "../../api/auth/getProfile.js";

export const useProfile = () => {
  const { data: user, loading: userLoading, refetch } = useQueryFix({
    query: useQuery({
      queryKey: 'user-profile',
      queryFn: () => getAccessToken() && getProfile(),
      onError: ()=> undefined,
      staleTime: 10000,
      refetchOnWindowFocus: false,
    }),
    transform: (data) => data.user,
  });

  return { user, userLoading, refetch};
};