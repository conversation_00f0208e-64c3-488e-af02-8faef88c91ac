import MainFooter from "../componets/MainFooter";
import {Outlet, useNavigate} from "react-router-dom";
import {useProfile} from "../../hooks/auth/useProfile.js";
import {useEffect} from "react";

export default function AccountLayout() {
    const navigate = useNavigate();
    const {user, userLoading} = useProfile();

    useEffect(() => {
        if(userLoading) return;

        if(!user) navigate("/")
    }, [user, userLoading]);

  return (
    <>
     <div className="main_wrapper d-flex flex-column">
        <Outlet />

        <div className="col d-none d-lg-block">
          <MainFooter />
        </div>
      </div>
    </>
  );
}
