import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {patchShop} from "../../api/shop/patchShop.js";
import {useEffect, useState} from "react";
import {uploadUrl} from "../../api/uploadUrl.js";
import {patchUser} from "../../api/auth/patchUser.js";
import {useProfile} from "../auth/useProfile.js";

const shopSchema = Yup.object().shape({
    email: Yup.string().email('Invalid email').required('Email is required'),
    firstName: Yup.string().max(100, 'Name is too long'),
    lastName: Yup.string(),
    avatar: Yup.mixed().nullable(),
    enable2FA: Yup.boolean(),
    shopName: Yup.string().max(100, 'Shop name is too long'),
    coverImage: Yup.mixed().nullable(),
    vacationMode: Yup.boolean(),
    sendEmailOnNewOrder: Yup.boolean(),
    showLocation: Yup.boolean(),
    phoneNumber: Yup.string().matches(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number'),
    links: Yup.array().of(
        Yup.object().shape({
            title: Yup.string(),
            url: Yup.string(),
        })
    ),
    addresses:Yup.array().of(
        Yup.object().shape({
            city: Yup.string(),
        })
    ),
    hideAdditionalInfo: Yup.boolean(),
    currentPassword: Yup.string().min(6, 'Password must be at least 6 characters'),
    newPassword: Yup.string().min(6, 'Password must be at least 6 characters'),
    repeatPassword: Yup.string()
        .min(6, 'Password must be at least 6 characters')
        .oneOf([Yup.ref('newPassword'), null], 'Passwords must match'),
    country: Yup.string(),
    companyTaxId: Yup.string(),
    company: Yup.string(),
    telephone: Yup.string()
        .matches(/^\+?[1-9]\d{1,14}$/, 'Invalid telephone number'),
    fax: Yup.string()
        .matches(/^\+?[1-9]\d{1,14}$/, 'Invalid fax number')
});

export default function useShopSettingForm({existingShop}) {
    const {user} = useProfile();
    const [initialValues, setInitialState] = useState({
        email: '',
        firstName: '',
        userName:'',
        lastName: '',
        avatar: null,
        phoneNumber: '',
        country: '',
        enable2FA: false,
        newPassword: '',
        repeatPassword:'',
        shopName: '',
        coverImage: null,
        vacationMode: false,
        sendEmailOnNewOrder: false,
        showLocation: false,
        links: [],
        addresses: [],
        hideAdditionalInfo: false,
        currentPassword: '',
        companyTaxId: '',
        company: '',
        telephone: '',
        fax: ''
    })

    useEffect(()=> {

        if(!user) return;

        const [name1, ...rest] = user.name.split(" ");
        setInitialState(prev=> ({
            ...prev,
            email: user.email,
            userName: user.name,
            phoneNumber: user.phoneNumber,
            avatar: user.avatar,
            firstName: name1,
            lastName: rest.join(","),
        }))
    }, [user])

    useEffect(() => {
        if(!existingShop) return;

        setInitialState(prev=> ({
            ...prev,
            newPassword: existingShop.newPassword,
            repeatPassword: existingShop.repeatPassword,
            shopName: existingShop.shopName,
            coverImage: existingShop.coverImage,
            vacationMode: existingShop.vacationMode,
            sendEmailOnNewOrder: existingShop.sendEmailOnNewOrder,
            showLocation: existingShop.showLocation,
            links: existingShop.links,
            hideAdditionalInfo: existingShop.hideAdditionalInfo,
            country: existingShop.seller.country,
            company: existingShop.company,
            companyTaxId: existingShop.companyTaxId,
            enable2FA: existingShop.seller.enable2FA,
            telephone: existingShop.telephone,
            fax: existingShop.fax,
            addresses: existingShop.addresses,
        }))
    }, [existingShop]);

    const { mutateAsync } = useMutation(patchShop, {
        onError: (error)=> showError(error),
    });

    const { mutateAsync:mutateUser } = useMutation(patchUser, {
        onError: (error)=> showError(error),
    });

    const uploadImage = async (image) => {
        if (image instanceof File) {
            const fileType = image.name.split('.').pop()?.toLowerCase() || 'unknown';
            const resignedResponse = await uploadUrl({ name: image.name, fileType });
            const { url: resignedUrl, path: filePath } = resignedResponse;

            await fetch(resignedUrl, {
                method: 'PUT',
                headers: { 'Content-Type': image.type, 'x-amz-acl': 'public-read' },
                body: image,
            });

            return filePath;
        }
        return image;
    };

    const queryClient = useQueryClient();

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: shopSchema,
        onSubmit: async (values, { setSubmitting }) => {

            const avatar = await uploadImage(values.avatar ?? null);
            const coverImage = await uploadImage(values.coverImage ?? null);

            let response;

            setSubmitting(true);
            if(user.role === "seller") response = await mutateAsync({...values, name: `${values.firstName} ${values.lastName}`, avatar, coverImage, _id: existingShop._id});
            else response = await mutateUser({...values, name: `${values.firstName} ${values.lastName}`, avatar, coverImage});
            setSubmitting(false);
            if (response) {
                queryClient.invalidateQueries('user-profile').finally()
                showSuccess("Request submitted successfully.");
            }
        },
    });

    return { formik };
}
