import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getFaq} from "../../api/faq/getFaq.js";

export const useFaq = () => {
    const { data: faq, loading: faqLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['faq'],
            queryFn: () => getFaq(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { faq, faqLoading};
};