/* eslint-disable react/prop-types */
import {Link} from "react-router-dom";
import Pagination from "../componets/utility/Pagination";
import MainHeader from "../componets/MainHeader";
import AccountSideBar from "../componets/account/AccountSideBar";
import {useEffect, useState} from "react";
import MainFooter from "../componets/MainFooter";
import {useSales} from "../../hooks/sales/useSales.js";
import Spinner from "../../components/common/Spinner.jsx";
import SaleCard from "../../components/sales/SaleCard.jsx";
import SaleRow from "../../components/sales/SaleRow.jsx";
import {generateSaleQuery} from "../../vbrae-utils/lib/misc.js";
import {useDebounce} from "../../hooks/common/useDebounce.js";
import SaleMobileRow from "../../components/sales/SaleMobileRow.jsx";

function SalesFilter({ isOpen, onClose, setFilters, createdDateTo, createdDateFrom, releaseDateFrom, releaseDateTo, productName, orderIncrementId, reservationId }) {

  const [fProduct, setFProduct] = useState("");
  const [forderId, setForderId] = useState("");
  const [fReserveId, setFReserveId] = useState("");

  const [cdFromType, setcdFromType] = useState("");
  const [cdToType, setcdToType] = useState("");
  const [rdFromType, setrdFromType] = useState("");
  const [rdToType, setrdToType] = useState("");

  const debounceProduct = useDebounce(fProduct, 500)
  const debouncedOrderId = useDebounce(forderId, 500);
  const debouncedReserveId = useDebounce(fReserveId, 500);

  useEffect(()=> {
    if(productName === "") setFProduct("");
    if(orderIncrementId === "") setForderId("")
    if(reservationId === "") setFReserveId("")
  }, [productName, orderIncrementId, reservationId])

  useEffect(() => {
    setFilters(prevState => ({...prevState,productName:debounceProduct, orderIncrementId:debouncedOrderId, reservationId: debouncedReserveId}));
  }, [debounceProduct, debouncedOrderId, debouncedReserveId]);

  return (
    <>
      <div
        className={
          "col acct_filter_cont flex-column mb-lg-4 " +
          (isOpen ? "d-flex" : "d-none d-lg-flex")
        }>
        <div className="col-auto row g-3 g-lg-2 mb-4 mb-lg-0">
          <p className="col d-flex d-lg-none cate_filter_title align-items-center gap-2 mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="1em"
              height="1em"
              viewBox="0 0 24 24">
              <g fill="none" fillRule="evenodd">
                <path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z" />
                <path
                  fill="currentColor"
                  d="M3 4.5A1.5 1.5 0 0 1 4.5 3h15A1.5 1.5 0 0 1 21 4.5v2.086A2 2 0 0 1 20.414 8L15 13.414v7.424a1.1 1.1 0 0 1-1.592.984l-3.717-1.858A1.25 1.25 0 0 1 9 18.846v-5.432L3.586 8A2 2 0 0 1 3 6.586z"
                />
              </g>
            </svg>
            Filter by
            <span
              onClick={onClose}
              className="d-flex justify-content-center align-items-center ms-auto">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M6.4 19L5 17.6l5.6-5.6L5 6.4L6.4 5l5.6 5.6L17.6 5L19 6.4L13.4 12l5.6 5.6l-1.4 1.4l-5.6-5.6z"></path>
              </svg>
            </span>
          </p>

          <div className="col-12 col-lg-4 col-xl">
            <div className="col-12 acct_box ">
              <input
                type="text"
                className="input_box"
                placeholder="Product name"
                value={fProduct}
                onChange={(e)=> setFProduct(e.target.value)}
              />
            </div>
          </div>
          <div className="col-12 col-lg-4 col-xl">
            <div className="col-12 acct_box ">
              <input
                type="text"
                className="input_box"
                placeholder="Order Increment ID"
                value={forderId}
                onChange={(e)=> setForderId(e.target.value)}
              />
            </div>
          </div>
          <div className="col-12 col-lg-4 col-xl">
            <div className="col-12 acct_box ">
              <input
                type={cdFromType}
                value={createdDateFrom}
                className="input_box"
                placeholder="Created date from"
                onFocus={()=> setcdFromType("date")}
                onBlur={()=> setcdFromType("text")}
                onChange={(e)=> setFilters(prev=> ({...prev, createdDateFrom: e.target.value}))}
              />
            </div>
          </div>
          <div className="col-12 col-lg-4 col-xl">
            <div className="col-12 acct_box ">
              <input
                type={cdToType}
                value={createdDateTo}
                className="input_box"
                placeholder="Created date to"
                onFocus={()=> setcdToType("date")}
                onBlur={()=> setcdToType("text")}
                onChange={(e)=> setFilters(prev=> ({...prev, createdDateTo: e.target.value}))}
              />
            </div>
          </div>
          <div className="col-12 col-lg-4 col-xl">
            <div className="col-12 acct_box ">
              <input
                type={rdFromType}
                value={releaseDateFrom}
                className="input_box"
                placeholder="Released date from"
                onFocus={()=> setrdFromType("date")}
                onBlur={()=> setrdFromType("text")}
                onChange={(e)=> setFilters(prev=> ({...prev, releaseDateFrom: e.target.value}))}
              />
            </div>
          </div>

          {/* Cloumn Brake */}
          <div className="w-100 d-none d-xl-block"></div>

          <div className="col-12 col-lg-4 col-xl">
            <div className="col acct_box ">
              <input
                type={rdToType}
                value={releaseDateTo}
                className="input_box"
                placeholder="Released date to"
                onFocus={()=> setrdToType("date")}
                onBlur={()=> setrdToType("text")}
                onChange={(e)=> setFilters(prev=> ({...prev, releaseDateTo: e.target.value}))}

              />
            </div>
          </div>
          <div className="col-12 col-lg-4 col-xl">
            <div className="col acct_box ">
              <input
                type="text"
                className="input_box"
                placeholder="Reservation ID"
                value={fReserveId}
                onChange={e=> setFReserveId(e.target.value)}
              />
            </div>
          </div>
          <div className="col-12 col-lg-4 col-xl">
            <select className="col acct_sel" onChange={e=> setFilters(prev=> ({...prev, status: e.target.value}))}>
              <option value="">Status</option>
            </select>
          </div>
          <div className="col-12 col-lg-4 col-xl">
            <select className="col acct_sel" onChange={e=> setFilters(prev=> ({...prev, mostRecent: e.target.value}))}>
              <option value="">Reservation freshness</option>
            </select>
          </div>
          <div className="col-auto">
            <div className="col-12 acct_box_icon">
              <span className="icon">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="m12 16l-5-5l1.4-1.45l2.6 2.6V4h2v8.15l2.6-2.6L17 11zm-8 4v-5h2v3h12v-3h2v5z"
                  />
                </svg>
              </span>
            </div>
          </div>
        </div>

        <div className="col-auto d-flex d-lg-none gap-2 cate_filter_btn_con mt-auto">
          <button type="button" className="col cate_filter_btn dark">
            Clear all
          </button>
          <button type="button" className="col cate_filter_btn">
            Apply filter
          </button>
        </div>
      </div>
    </>
  );
}

export default function Sales() {
  const breadCrums = [
    {
      title: "Sales",
      url: "/account/sales",
    },
  ];

  const breadCrumsList = breadCrums?.map((item, index) => {
    return (
      <Link to={item.url} key={index}>
        <p className="crumb_link">/ {item.title}</p>
      </Link>
    );
  });

  return (
    <>
      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={"sales"} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadCrums}
            activeLink={"sales"}
          />
          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <RenderSales breadCrumsList={breadCrumsList} />
          </div>
        </div>
      </div>
    </>
  );
}

const initialState = {
  productName: "",
  orderIncrementId: "",
  mostRecent: "",
  releaseDate: "",
  status: "",
  releaseDateFrom: "",
  releaseDateTo: "",
  createdDateFrom: "",
  createdDateTo: "",
  reservationId: ""
}

const RenderSales = ({breadCrumsList})=>{

  const [filters, setFilters] = useState(initialState);
  const [page, setPage] = useState(1);

  const {salesData, salesLoading} = useSales({query: generateSaleQuery(filters)});

  const [isFilterOpen, setIsFilterOpen] = useState(false);

  function OpenFilter() {
    setIsFilterOpen(true);
  }
  function CloseFilter() {
    setIsFilterOpen(false);
  }

  const saleOptions = [
    {label: "All", value: salesData?.totalOrders},
    {label: "Active", value: salesData?.reviewOrders},
    {label: "Completed", value: salesData?.completedOrders},
    {label: "Cancelled", value: salesData?.cancelledOrders},
  ]

  return (
      <>
        <div className=" d-lg-none justify-content-between align-items-center mb-3">
          <div className="d-flex align-items-center gap-2">
            <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
            </Link>
            {breadCrumsList}
          </div>
        </div>

        <div className="acct_cont ">
          <div className="col">
            <div className="col row row-cols-2 row-cols-lg-4 g-2 justify-content-between mb-3">
              {saleOptions.map(item => <SaleCard key={item._id} {...item}/>)}
            </div>

            <SalesFilter
                isOpen={isFilterOpen}
                onClose={CloseFilter}
                setFilters={setFilters}
                {...filters}
            />

            <div
                className="col d-xl-flex flex-row-reverse align-items-start align-items-xl-center gap-3 gap-xl-4 mb-4">
              <div className="col col-xl-3 d-flex gap-2 align-items-center mb-3 mb-xl-0">
                <p
                    onClick={OpenFilter}
                    className="col d-flex d-lg-none cate_filter_tigger align-items-center gap-2">
                  <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                    <g fill="none" fillRule="evenodd">
                      <path
                          d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"/>
                      <path
                          fill="currentColor"
                          d="M3 4.5A1.5 1.5 0 0 1 4.5 3h15A1.5 1.5 0 0 1 21 4.5v2.086A2 2 0 0 1 20.414 8L15 13.414v7.424a1.1 1.1 0 0 1-1.592.984l-3.717-1.858A1.25 1.25 0 0 1 9 18.846v-5.432L3.586 8A2 2 0 0 1 3 6.586z"
                      />
                    </g>
                  </svg>
                  Filter by
                  <span className="d-flex justify-content-center align-items-center ms-auto">
                        6
                      </span>
                </p>
                <select className="col col-lg-6 col-xl cate_filter_sort_sel">
                  <option value="">Most recent</option>
                </select>
              </div>

              <div className="col d-flex gap-2 overflow-auto hide_scroll">
                    <span className="cate_filter_tag" onClick={() => {
                      setFilters(initialState);
                    }}>
                      Clear all
                      <svg
                          className="ms-1"
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>
                {Object.entries(filters).map(([key, value]) => {
                  if (key === "productName" && value) {
                    return (
                        <span className="cate_filter_tag" key={key} onClick={()=> setFilters(prevState => ({...prevState, [key]: ""}))}>
                                Product: {value}
                          <svg
                              className="ms-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 32 32">
                                  <path
                                      fill="currentColor"
                                      d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                  />
                                </svg>
                            </span>
                    )
                  } else if (key === "orderIncrementId" && value) {
                    return (
                        <span className="cate_filter_tag" key={key}
                              onClick={() => setFilters(prevState => ({...prevState, [key]: ""}))}>
                                Order: {value}
                          <svg
                              className="ms-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 32 32">
                                  <path
                                      fill="currentColor"
                                      d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                  />
                                </svg>
                            </span>
                    )
                  } else if (key === "createdDateFrom" && value) {
                    return (
                        <span className="cate_filter_tag" key={key}
                              onClick={() => setFilters(prevState => ({...prevState, [key]: ""}))}>
                                Created From: {value}
                          <svg
                              className="ms-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 32 32">
                                  <path
                                      fill="currentColor"
                                      d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                  />
                                </svg>
                            </span>
                    )
                  } else if (key === "createdDateTo" && value) {
                    return (
                        <span className="cate_filter_tag" key={key}
                              onClick={() => setFilters(prevState => ({...prevState, [key]: ""}))}>
                                Created To: {value}
                          <svg
                              className="ms-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 32 32">
                                  <path
                                      fill="currentColor"
                                      d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                  />
                                </svg>
                            </span>
                    )
                  } else if (key === "reservationId" && value) {
                    return (
                        <span className="cate_filter_tag" key={key}
                              onClick={() => setFilters(prevState => ({...prevState, [key]: ""}))}>
                                Reservation: {value}
                          <svg
                              className="ms-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 32 32">
                                  <path
                                      fill="currentColor"
                                      d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                  />
                                </svg>
                            </span>
                    )
                  } else if (key === "releaseDateTo" && value) {
                    return (
                        <span className="cate_filter_tag" key={key}
                              onClick={() => setFilters(prevState => ({...prevState, [key]: ""}))}>
                                Release To: {value}
                          <svg
                              className="ms-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 32 32">
                                  <path
                                      fill="currentColor"
                                      d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                  />
                                </svg>
                            </span>
                    )
                  } else if (key === "releaseDateFrom" && value) {
                    return (
                        <span className="cate_filter_tag" key={key}
                              onClick={() => setFilters(prevState => ({...prevState, [key]: ""}))}>
                                Release From: {value}
                          <svg
                              className="ms-1"
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 32 32">
                                  <path
                                      fill="currentColor"
                                      d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                  />
                                </svg>
                            </span>
                    )
                  }
                })}
              </div>
            </div>

            {!salesData || salesLoading ? <Spinner/> : (
                <>
                  <hr className="acct_fil_line mb-0"/>
                  {/* Mobile Table */}
                  <div className="col d-lg-none mb-4">
                    {salesData.orders.map(item => <SaleMobileRow key={item._id} {...item} />)}
                  </div>
                  {/* Desktop Table */}
                  <div className="table-responsive d-none d-lg-block">
                    <table className="table table-borderless acct_table mb-3">
                      <thead>
                      <tr>
                        <th className="acct_table_head">Name</th>
                        <th className="acct_table_head d-flex align-items-center gap-1">
                          IWTR
                          <span className="icon">
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                xmlns="http://www.w3.org/2000/svg">
                              <path
                                  d="M3 5V6.66667H7.66667V5H3ZM3 9.16667V10.8333H12.3333V9.16667H3ZM3 13.3333V15H17V13.3333H3Z"/>
                            </svg>
                          </span>
                        </th>
                        <th className="acct_table_head col-1">BID</th>
                        <th className="acct_table_head">ORDER INCREMENT ID</th>
                        <th className="acct_table_head">RESERVATION ID</th>
                        <th className="acct_table_head">STATUS</th>
                        <th className="acct_table_head">CREATED</th>
                        <th className="acct_table_head">RELEASED</th>
                        <th className="acct_table_head"></th>
                      </tr>
                      </thead>
                      <tbody>
                      {salesData.orders.map(item => <SaleRow key={item._id} {...item} />)}
                      </tbody>
                    </table>
                  </div>
                  <div className="col d-flex justify-content-center">
                    {salesData.totalPages && <Pagination
                        totalPages={salesData.totalPages}
                        currentPage={page}
                        pageClick={(page) => setPage(page)}
                        nextPage={() => setPage(page + 1)}
                        prevPage={() => setPage(page - 1)}/>}
                  </div>
                </>
            )}
          </div>
        </div>

        <div className="col d-lg-none mt-4">
          <MainFooter/>
        </div>
      </>
  )
}