import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {postReview} from "../../api/rating/postReview.js";

const schema = Yup.object().shape({
    stars:Yup.number().positive(),
    content: Yup.string().required("Comment is required")
});

export default function useRatingForm({onClose, data}) {

    const queryClient = useQueryClient();

    const initialValues = {
        stars: 1,
        content: '',
    }

    const { mutateAsync } = useMutation(postReview, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: schema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync({...values, ...data});
            setSubmitting(false);
            resetForm();
            if (response) {
               showSuccess("Review Updated Successfully");
               // queryClient.invalidateQueries("notifications");
               onClose();
            }
        },
    });

    return { formik };
}
