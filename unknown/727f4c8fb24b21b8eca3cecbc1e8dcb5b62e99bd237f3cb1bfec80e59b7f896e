import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {useEffect, useState} from "react";
import {patchUser} from "../../api/auth/patchUser.js";
import {uploadUrl} from "../../api/uploadUrl.js";

const accountSchema = Yup.object().shape({
    email: Yup.string()
        .trim()
        .email('Invalid email address')
        .required('Email address is required'),
    username: Yup.string()
        .trim(),
        // .required('Username is required'),
    firstName: Yup.string()
        .trim(),
        // .required('First name is required'),
    lastName: Yup.string()
        .trim(),
        // .required('Last name is required'),
    phone: Yup.string()
        .trim()
        .matches(/^\d*$/, 'Phone number must be numeric'),
        // .required('Phone number is required'),
    country: Yup.string()
        .trim(),
        // .required('Country is required'),
    avatar: Yup.mixed().nullable(),
    coverImage: Yup.mixed().nullable(),
    receiveEmails: Yup.boolean(),
    showLocation: Yup.boolean(),
});

export default function useProfileForm({existingProfile}) {



    const queryClient = useQueryClient();
    const [initialValues, setInitialValues] = useState({
        email: '',
        username: '',
        firstName: '',
        lastName: '',
        phone: '',
        country: '',
        avatar: null,
        coverImage: null,
        receiveEmails: false,
        showLocation: false,
    })

    useEffect(() => {
        if(!existingProfile) return;
        setInitialValues({
            email: existingProfile.email,
            username: existingProfile.username,
            firstName: existingProfile.firstName,
            lastName: existingProfile.lastName,
            phone: existingProfile.phone,
            country: existingProfile.country,
            receiveEmails: existingProfile.receiveEmails,
            showLocation: existingProfile.showLocation,
            avatar: existingProfile.avatar ?? null,
            coverImage: existingProfile.coverImage ?? null,
        })

    }, [existingProfile]);



    const { mutateAsync } = useMutation(patchUser, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: accountSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {

            const avatar = await uploadImage(values.avatar ?? null);
            const coverImage = await uploadImage(values.coverImage ?? null);

            setSubmitting(true);
            const response = await mutateAsync({...values, avatar, coverImage});
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess("Profile updated!");
                await queryClient.invalidateQueries("user-profile");
            }
        },
    });

    return { formik };
}
