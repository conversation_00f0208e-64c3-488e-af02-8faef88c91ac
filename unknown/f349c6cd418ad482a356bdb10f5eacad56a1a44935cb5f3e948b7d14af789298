import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {setAccessToken, showError} from "../../vbrae-utils/index.js";
import {postRegisterForm} from "../../api/auth/postRegisterForm.js";
import {useContext} from "react";
import {ModalContext} from "../../store/ModalContext.jsx";

const registerSchema = Yup.object().shape({
    name:Yup.string().trim()
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Name is required'),
    email:Yup.string().trim()
        .email('Wrong email format')
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Email is required'),
    password: Yup.string()
        .min(8, 'Password must be at least 8 characters')
        .max(38, 'Password must not exceed 38 characters')
        .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
        .matches(/[a-z]/, 'Password must contain at least one lowercase letter')
        .matches(/\d/, 'Password must contain at least one number')
        .matches(/[!@#$%^&*(),.?":{}|<>]/, 'Password must contain at least one special character')
        .required('Password is required'),
    confirmPassword: Yup.string()
        .oneOf([Yup.ref('password'), null], 'Passwords must match')
        .required('Confirm Password is required'),
    acceptTOS: Yup.boolean().isTrue('Must agree to terms'),
});

export default function useRegisterForm() {
    const queryClient = useQueryClient();
    const { CloseModal } = useContext(ModalContext);
    const initialValues = {
        name: '',
        email: '',
        password: '',
        confirmPassword: '',
        acceptTOS: false
    }

    const { mutateAsync } = useMutation(postRegisterForm, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: registerSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const geoResponse = await fetch('https://geolocation-db.com/json/');
            const data = await geoResponse.json();
            const response = await mutateAsync({...values, ipAddress: data.IPv4, country: data.country_name});
            setSubmitting(false);
            resetForm();
            if (response) {
                setAccessToken(response.token, response.user._id, response.clearSaleToken);
                queryClient.invalidateQueries(['user-profile']).finally()
                CloseModal()
            }
        },
    });

    return { formik };
}
