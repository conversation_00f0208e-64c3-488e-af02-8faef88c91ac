import {Link} from "react-router-dom";
import Spinner from "../components/common/Spinner.jsx";
import {usePlaceOrder} from "../hooks/clearSale/usePlaceOrder.js";
import {useOrders} from "../hooks/orders/useOrders.js";
import {useProfile} from "../hooks/auth/useProfile.js";
import {getSalesToken} from "../vbrae-utils/index.js";
import {useEffect, useState} from "react";
import MainHeader from "./componets/MainHeader.jsx";
import MainFooter from "./componets/MainFooter.jsx";
import MenuSideBar from "./componets/MenuSideBar.jsx";

const sampleOrderData = {
    orderId: "T2LA",
    items: [
        {
            image: "/path-to-image.jpg",
            name: "DYNASTY WARRIORS 8: Xtreme Legends Complete Edition Steam CD Key",
            category: "Super Games",
            price: "31.64",
            quantity: 1,
            taxIncluded: "0.00",
            discount: "$1.90",
            rowTotal: "29.74"
        }
    ],
    summary: {
        subtotal: "31.64",
        discount: "1.90",
        platformDev: "1.64",
        paymentFee: "1.66",
        taxIncluded: "0.00",
        total: "33.04"
    }
};


export default function ThankYou() {

    const { orders } = useOrders();
    const { user } = useProfile();

    const [payload, setPayload] = useState({});

    useEffect(() => {

        console.log("Orders: ", orders, user);

        if (!orders?.length || !user || user.isKycVerified === "approved") return;

        const orderObj = orders[0];
        const userObj = user;
        const name = userObj.name || "Customer";
        const userAddress = userObj.address[0];

        const newPayload = {
            LoginToken: getSalesToken(),
            AnalysisLocation: "USA",
            Orders: [
                {
                    ID: orderObj.orderNumber,
                    Date: orderObj.createdAt,
                    Email: userObj.email,
                    TotalItems: orderObj.subtotal,
                    TotalOrder: orderObj.total,
                    IP: userObj.ipAddress,
                    Currency: "USD",
                    Payments: [
                        {
                            Date: "2025-06-01T14:30:00",
                            Amount: orderObj.total,
                            Type: 1
                        }
                    ],
                    ShippingData: {
                        ID: userAddress?._id,
                        Type: 1,
                        Name: name,
                        Address: {
                            Street: userAddress?.street,
                            Number: userAddress?.number || "N/A",
                            City: userAddress?.city,
                            State: userAddress?.state,
                            ZipCode: userAddress?.zip,
                            Country: userAddress?.country
                        },
                        Phones: [
                            {
                                Type: 1,
                                DDD: userAddress?.phone?.slice(0, 2) || "00",
                                Number: userAddress?.phone?.slice(2) || "000000000"
                            }
                        ]
                    },
                    BillingData: {
                        ID: userAddress?._id,
                        Type: 1,
                        Name: name,
                        Address: {
                            Street: userAddress?.street,
                            Number: userAddress?.number || "N/A",
                            City: userAddress?.city,
                            State: userAddress?.state,
                            ZipCode: userAddress?.zip,
                            Country: userAddress?.country
                        },
                        Phones: [
                            {
                                Type: 1,
                                DDD: userAddress?.phone?.slice(0, 2) || "00",
                                Number: userAddress?.phone?.slice(2) || "000000000"
                            }
                        ]
                    },
                    Items: orderObj.items?.map((item, index) => ({
                        ID: `PROD-${index + 1}`,
                        Name: item.offer.name,
                        ItemValue: item.price,
                        Qty: item.quantity
                    })) || [],
                    Origin: "Vbrae",
                    SessionID: `session-${Date.now()}-${Math.floor(Math.random() * 1000000)}`
                }
            ]
        };

        setPayload(newPayload);
    }, [orders, user]);

    const { placeOrderLoading } = usePlaceOrder(payload);

    if (placeOrderLoading) {
        return (
            <div className="fixed inset-0 d-flex justify-content-center align-items-center w-100 h-100">
                <Spinner />
            </div>
        );
    }

    return (
        <div className="d-flex main_house_con">
            <MenuSideBar makeShort={true} activeLink={'cart'} />

            <div className="col housing_con d-flex flex-column">
                <MainHeader activeLink={'cart'} />

                <div className="housing d-flex gap-1 position-relative">
                    <div id="scrollable-section" className="col main_section">
                        <div className="d-flex align-items-center gap-2 mb-3">
                            <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                            </Link>
                            <Link>
                                <p className="crumb_link">/ Cart</p>
                            </Link>
                        </div>
                        <section className="">
                            <div className="container py-4 py-lg-8 text-center position-relative overflow-hidden">
                                <p className="banner_title"> Thank You! Your order <span className="text-warning">T2LA</span> is PROCESSING.</p>
                                <p className="main_sub_text">Delivery is in progress. Estimated Delivery time up to: 20 minutes</p>
                            </div>
                            <div className="main_cont d-flex gap-4 position-relative">
                                <div className="w-100">
                                    <p className="details_tit">ORDER : <span className="">T2LA</span></p>
                                    <div className="px-3 py-1 rounded-pill border d-inline-block small text-white">
                                        Processing
                                    </div>
                                </div>
                            </div>

                            <div className="table-responsive">
                                <table className="w-100 table table-borderless acct_table order-table">
                                    <thead>
                                    <tr>
                                        <th className="acct_table_head" style={{width: '60px'}}></th>
                                        <th className="acct_table_head">Product</th>
                                        <th className="acct_table_head text-center">Price</th>
                                        <th className="acct_table_head text-center">Quantity</th>
                                        <th className="acct_table_head text-center">Tax included</th>
                                        <th className="acct_table_head text-center">Discount</th>
                                        <th className="acct_table_head text-center">Row Total</th>
                                        <th className="acct_table_head text-center">Status</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {sampleOrderData.items.map((item, index) => (
                                        <tr key={index} className="acct_table_row">
                                            <td className="acct_table_data">
                                                <img
                                                    src={item.image}
                                                    alt={item.name}
                                                    className="acct_table_data_img order-item-img"
                                                />
                                            </td>
                                            <td className="acct_table_data">
                                                <div className="product-details">
                                                    <h6 className="product-title mb-1">{item.name}</h6>
                                                    <small className="text-muted">{item.category}</small>
                                                </div>
                                            </td>
                                            <td className="acct_table_data text-center">
                                                <strong>${item.price}</strong>
                                            </td>
                                            <td className="acct_table_data text-center">
                                                <span className="quantity-badge">{item.quantity}</span>
                                            </td>
                                            <td className="acct_table_data text-center">
                                                ${item.taxIncluded}
                                            </td>
                                            <td className="acct_table_data text-center text-danger">
                                                -{item.discount}
                                            </td>
                                            <td className="acct_table_data text-center">
                                                <strong>${item.rowTotal}</strong>
                                            </td>
                                            <td className="acct_table_data text-center">
                                    <span className="order-status-badge processing-small">
                                        PROCESSING
                                    </span>
                                            </td>
                                        </tr>
                                    ))}
                                    </tbody>
                                </table>
                            </div>
                        </section>

                        <div className="col d-lg-none">
                            <MainFooter/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}