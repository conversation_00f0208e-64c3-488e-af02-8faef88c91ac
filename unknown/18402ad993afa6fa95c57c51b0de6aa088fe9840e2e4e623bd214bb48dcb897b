import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getShopById} from "../../api/shop/getShopById.js";

export const useShopDetails = ({_id}) => {
    const { data: shopDetails, loading: shopDetailsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['shop', _id],
            queryFn: () => getShopById({_id}),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: !!_id
        }),
        transform: (data) => data.data,
    });

    return { shopDetails, shopDetailsLoading};
};