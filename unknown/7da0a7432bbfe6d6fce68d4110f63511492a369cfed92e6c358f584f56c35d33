import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getSellerOffers} from "../../api/offers/getSellerOffers.js";

export const useSellerOffers = ({query}) => {
    const { data: offers, loading: offersLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['game-item','seller-offers', query],
            queryFn: () => getSellerOffers({query}),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { offers, offersLoading};
};