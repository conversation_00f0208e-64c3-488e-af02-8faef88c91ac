import * as Yup from 'yup';
import {useMutation} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {postTicket} from "../../api/ticket/postTicket.js";
import {uploadUrl} from "../../api/uploadUrl.js";
import {inquiryOptions, supportSubjects} from "../../constant/ticketOptions.js";

const inquirySchema = Yup.object().shape({
    email: Yup.string().email('Invalid email').required('Email is required'),
    subject: Yup.string().required('Subject is required'),
    inquiryType: Yup.string().required('Inquiry type is required'),
    description: Yup.string().required('Description is required'),
    attachments: Yup.array().of(Yup.mixed()).nullable(),
});

export default function useTicketForm({userName, handleSuccess}) {
    const initialValues = {
        email: '',
        subject: supportSubjects[0].title,
        inquiryType: inquiryOptions[0].title,
        description: '',
        attachments: []
    }

    const uploadImage = async (image) => {
        if (image instanceof File) {
            const fileType = image.name.split('.').pop()?.toLowerCase() || 'unknown';
            const resignedResponse = await uploadUrl({ name: image.name, fileType });
            const { url: resignedUrl, path: filePath } = resignedResponse;

            await fetch(resignedUrl, {
                method: 'PUT',
                headers: { 'Content-Type': image.type, 'x-amz-acl': 'public-read' },
                body: image,
            });

            return filePath;
        }
        return image;
    };

    const { mutateAsync } = useMutation(postTicket, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: inquirySchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);

            const imageUrls = await Promise.all(values.attachments.map(async (attachment) => await uploadImage(attachment)))

            const response = await mutateAsync({...values, name: userName, attachments: imageUrls});
            setSubmitting(false);
            resetForm();
            if (response) {
                handleSuccess();
                showSuccess("Request submitted successfully.");
            }
        },
    });

    return { formik };
}
