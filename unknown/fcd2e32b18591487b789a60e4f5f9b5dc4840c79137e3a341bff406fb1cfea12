import {useQuery} from "react-query";
import {setPaymentInfo, useQueryFix} from "../../vbrae-utils/index.js";
import {createPayment} from "../../api/payment/create-payment.js";

export const useCreatePayment = ({amount}) => {
    const { data: viewCheckoutUrl} = useQueryFix({
        query: useQuery({
            queryKey: ['payment', amount],
            queryFn: () => createPayment({amount}),
            refetchOnWindowFocus: false,
            enabled:amount > 0,
            onSuccess: (r) => setPaymentInfo(r.paymentUrl),
        }),
        transform: (data) => data.viewCheckoutUrl,
    });

    return { viewCheckoutUrl};
};