import {Pagination} from "swiper/modules";
import {Swiper, SwiperSlide} from "swiper/react";
import GameItem from "../GameItem.jsx";
import {useSellerOffers} from "../../../hooks/offers/useSellerOffers.js";
import Spinner from "../../../components/common/Spinner.jsx";

export default function SimilarOffers({category, subCategory}){

    const {offers, offersLoading} = useSellerOffers({query: `category=${category}&subcategory=${subCategory}&limit=10`});

    if(!offers || offersLoading) {
        return <Spinner />
    }

    return (
        <Swiper
            modules={[Pagination]}
            spaceBetween={10}
            slidesPerView={"auto"}
            onSwiper={(swiper) => {
                swiper.wrapperEl.classList.add("game_slider_con");
            }}
            pagination={{
                el: ".custom_pagination",
                clickable: true,
                renderBullet: function (index, className) {
                    return `<span class="${className} custom_bullet"></span>`;
                },
            }}>
            {offers.data.map((item, index) => (
                <SwiperSlide style={{width: "auto"}} key={index}>
                    <GameItem {...item}/>
                </SwiperSlide>
            ))}

            <div className="custom_pagination d-flex justify-content-center mt-4"></div>
        </Swiper>
    )
}