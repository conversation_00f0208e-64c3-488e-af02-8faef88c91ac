import {Link, useParams} from "react-router-dom";
import AccountSideBar from "../componets/account/AccountSideBar";
import MainHeader from "../componets/MainHeader";
import KeyModal from "../componets/modals/KeyModal";
import RateModal from "../componets/modals/RateModal";
import { useState } from "react";
import ReportModal from "../componets/modals/ReportModal";
import OrderAccordion from "../componets/utility/OrderAccordion";
import MainFooter from "../componets/MainFooter";
import CountDownTimer from "../componets/utility/CountDownTimer";
import {useClientOrders} from "../../hooks/orders/useClientOrders.js";
import Spinner from "../../components/common/Spinner.jsx";
import {formatDateTime} from "../../vbrae-utils/lib/time.js";

export default function OrderDetails() {
  const breadCrums = [
    {
      title: "Orders",
      url: "/account/orders",
    },
    {
      title: "Order #20074",
      url: "/account/order-details",
    },
  ];

  const breadCrumsList = breadCrums?.map((item, index) => {
    return (
      <Link to={item.url} key={index}>
        <p className="crumb_link">/ {item.title}</p>
      </Link>
    );
  });

  return (
    <>
      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={"orders"} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadCrums}
            activeLink={"orders"}
          />

          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <div className=" d-lg-none justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-2">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                {breadCrumsList}
              </div>
            </div>
            <RenderOrderDetails />

            <div className="col d-lg-none mt-4">
              <MainFooter />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}


const RenderOrderDetails = () => {

  const time = new Date();
  time.setSeconds(time.getSeconds() + 172800); // 48 hours

  const [isRateModalOpen, setIsRateModalOpen] = useState(false);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [isKeyModalOpen, setIsKeyModalOpen] = useState(false);
  const [item, setItem] = useState({});

  const {id} = useParams();
  const {orders, orderLoading} = useClientOrders({query: "", id});

  function CloseRateModal() {
    setIsRateModalOpen(false);
  }
  function CloseReportModal() {
    setIsReportModalOpen(false);
  }
  function CloseKeyModal() {
    setIsKeyModalOpen(false);
  }

  function OpenRateModal() {
    setIsRateModalOpen(true);
  }
  function OpenReportModal() {
    setIsReportModalOpen(true);
  }
  function OpenKeyModal() {
    setIsKeyModalOpen(true);
  }

  if(!orders || orderLoading) return <Spinner />

  const [createdA, createdB] = formatDateTime(orders.createdAt);

  const [updatedA, updatedB] = formatDateTime(orders.updatedAt);

  return (
      <>
        <ReportModal isOpen={isReportModalOpen} onClose={() => CloseReportModal()} order={orders} />
        <RateModal isOpen={isRateModalOpen} onClose={() => CloseRateModal()} data={{orderNumber: orders.orderNumber, offerId: item._id}} />
        <KeyModal isOpen={isKeyModalOpen} onClose={CloseKeyModal} OpenRateModal={OpenRateModal} item={item} orderStatus={orders.status}/>
        <div className="acct_cont">
          <p className="acct_offer_title ">Orders #20074</p>
          <hr className="acct_fil_line my-3"/>
          <div className="col d-flex flex-wrap gap-3 mb-5">
            <div className="col-12 col-xl  acct_offer_cont">
              <div className="col row row-cols-2 row-cols-md-3 g-4 mb-4">
                <div className="col">
                  <p className="acct_head_sm mb-1">Status</p>
                  <div className="d-flex flex-wrap gap-2 align-items-center">
                        <span className={`acct_table_data_tag text-capitalize ${orders.status === "completed" ? "green" : (orders.status === "cancelled" ? "red" : "blue")}`}>
                          {orders.status}
                        </span>
                    {orders.status === "processing" && <CountDownTimer expiryTimestamp={time}/>}
                  </div>
                </div>
                <div className="col">
                  <p className="acct_head_sm mb-1">Payment Status</p>
                  <p className="acct_head_s text-capitalize">Payment {orders.status}</p>
                </div>
                <div className="col">
                  <p className="acct_head_sm mb-1">Payment Method </p>
                  <p className="acct_head_s">Swedbank Pay</p>
                </div>
                <div className="col">
                  <p className="acct_head_sm mb-1">Date</p>
                  <p className="acct_head_s">{createdA} / {createdB}</p>
                </div>
                <div className="col">
                  <p className="acct_head_sm mb-1">Update</p>
                  <p className="acct_head_s">{updatedA} / {updatedB}</p>
                </div>
              </div>
              <hr className="acct_fil_line2 my-3"/>

              {orders.items.map((item, index) => {
                const [updatedA, updatedB] = formatDateTime(item.offerDetails.updatedAt);
                return (
                    <div className="col mb-4" key={index}>
                      <OrderAccordion
                          isActive={false}
                          trigger={({isOpen, ToggleAccordion}) => (
                              <div className="col">
                                <div
                                    className="col d-flex flex-wrap gap-3 justify-content-between align-items-center mb-3">
                                  <div className="col-12 col-md-8 d-flex align-items-center gap-3">
                                    <img
                                        src={item.offerDetails.coverImage}
                                        alt=""
                                        className="acct_sale_img"
                                    />
                                    <div className="col">
                                      <p className="acct_head_s">
                                        {item.offerDetails.name}
                                        <span className="text-capitalize"> {item.offerDetails.category}</span>
                                        {item.offerDetails.instantDelivery ? " Digital Key " : " CD Key "}
                                        <span className="text-uppercase">{item.offerDetails.region}</span>
                                      </p>
                                      <span className={`d-inline-block acct_table_data_tag green mt-1 text-capitalize ${item.status === "completed" ? "green" : (item.status === "cancelled" ? "red" : "blue")}`}>
                                        {item.status}
                                      </span>
                                    </div>
                                  </div>
                                  <div className="col col-md-auto d-flex gap-4 justify-content-between align-items-center">
                                    <span className="acct_offer_qaun d-flex justify-content-center align-items-center">
                                      {item.quantity}
                                    </span>

                                    {item.quantity === 1 ? (<button
                                        type="button"
                                        disabled={item.status !== "completed"}
                                        className="col acct_offer_btn3 mx-auto"
                                        onClick={() => {
                                          setItem({...item.offerDetails, orderNumber:orders.orderNumber, keys: item.keys})
                                          OpenKeyModal()
                                        }}>
                                      {item.status === "completed" ? "Show My Key" : (item.status === "cancelled" ? "Cancelled" : "Coming Soon")}
                                    </button>) : (
                                        <div
                                            className="acct_offer_arrow d-flex gap-1 justify-content-center align-items-center"
                                            role="button"
                                            onClick={() => ToggleAccordion()}>
                                          {isOpen ? "Close" : "Expand"}
                                          <span className="icon">
                                  {isOpen ? (
                                      <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="1em"
                                          height="1em"
                                          viewBox="0 0 24 24">
                                        <path
                                            fill="none"
                                            stroke="currentColor"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="m5 15l7-7l7 7"></path>
                                      </svg>
                                  ) : (
                                      <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="1em"
                                          height="1em"
                                          viewBox="0 0 24 24">
                                        <path
                                            fill="none"
                                            stroke="currentColor"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth={2}
                                            d="m19 9l-7 7l-7-7"></path>
                                      </svg>
                                  )}
                                </span>
                                        </div>
                                    )}
                                  </div>
                                </div>

                                <div className="col row row-cols-2 row-cols-md-4 g-4">
                                  <div className="col">
                                    <p className="acct_head_sm mb-1">Total</p>
                                    <p className="acct_offer_text_big">${orders.total?.toFixed(2)}</p>
                                  </div>

                                  <div className="col">
                                    <p className="acct_head_sm mb-1">Seller</p>
                                    <Link to={"/shop-details"}>
                                      <p className="acct_head_s blue">{item.offerDetails.sellerDetails.name}</p>
                                    </Link>
                                  </div>
                                  <div className="col">
                                    <p className="acct_head_sm mb-1">Update</p>
                                    <p className="acct_head_s">{updatedA} / {updatedB}</p>
                                  </div>
                                  <div className="col">
                                    <p className="acct_head_sm mb-1">Your Rating</p>
                                    <p className="acct_head_s d-flex gap-2 align-items-center">
                                    <span className="acct_head_s blue">
                                      <svg
                                          xmlns="http://www.w3.org/2000/svg"
                                          width="1em"
                                          height="1em"
                                          viewBox="0 0 24 24">
                                        <path
                                            fill="currentColor"
                                            d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"></path>
                                      </svg>
                                    </span>
                                      -
                                      <span
                                          className="acct_table_data_tag gray"
                                          role="button"
                                          onClick={() => {
                                            setItem({...item.offerDetails, orderNumber:orders.orderNumber})
                                            OpenRateModal()
                                          }}>
                                        Rate Seller
                                      </span>
                                    </p>
                                  </div>
                                </div>
                              </div>
                          )}
                          content={
                            <>
                              {Array.from({length: item.quantity}).map((_, index) => (
                                  <div className="col" key={index}>
                                    <hr className="acct_fil_line2 my-3"/>
                                    <div className="col d-flex gap-3 justify-content-between align-items-center">
                                      <div className="col">
                                        <p className="acct_head_s">
                                          {item.offerDetails.name}
                                          <span className="text-capitalize"> {item.offerDetails.category}</span>
                                          {item.offerDetails.instantDelivery ? " Digital Key " : " CD Key "}
                                          <span className="text-uppercase">{item.offerDetails.region}</span>
                                        </p>
                                        <p className="acct_offer_text_big">${item.offerDetails.expectedPrice?.toFixed(2)}</p>
                                      </div>

                                      <button
                                          type="button"
                                          disabled={item.status !== "completed"}
                                          className="col-auto acct_offer_btn3"
                                          onClick={() => OpenKeyModal()}>
                                        {item.status !== "completed" ? "Coming Soon" : "Show My Key"}
                                      </button>
                                    </div>
                                  </div>
                              ))}
                            </>
                          }
                      />
                      {orders.items.length > 1 && <hr className="acct_fil_line2 mt-3"/>}
                    </div>
                )
              })}
            </div>
            <div className="col-12 col-xl-4 ">
              <div className="col acct_offer_cont dark mb-3">
                <p className="acct_head_smm mb-4">Order summary</p>

                <p className="cart_sum_text d-flex justify-content-between align-items-center mb-2">
                  Subtotal ({orders.items.length} item) <span className="white">${orders.subtotal}</span>
                </p>
                <p className="cart_sum_text d-flex justify-content-between align-items-center mb-2">
                  Shipping <span className="white">$0</span>
                </p>

                <p className="cart_sum_head d-flex justify-content-between align-items-center mt-4">
                  Total <span>${orders.total?.toFixed(2)}</span>
                </p>
              </div>
              <div className="d-flex gap-2">
                <button
                    type="button"
                    className="col acct_offer_btn2 d-flex justify-content-center gap-2 align-items-center">
                      <span className="icon">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <path
                              fill="currentColor"
                              fillRule="evenodd"
                              d="M4 3a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v18a1 1 0 0 1-1.65.76l-1.033-.885a1 1 0 0 0-1.301 0l-1.032.884a1 1 0 0 1-1.302 0l-1.031-.884a1 1 0 0 0-1.302 0l-1.031.884a1 1 0 0 1-1.302 0l-1.032-.884a1 1 0 0 0-1.301 0l-1.032.884A1 1 0 0 1 4 21zm5 3a1 1 0 0 0 0 2h6a1 1 0 1 0 0-2zm0 4a1 1 0 1 0 0 2h6a1 1 0 1 0 0-2zm1 5a1 1 0 0 1 1-1h4a1 1 0 1 1 0 2h-4a1 1 0 0 1-1-1"
                              clipRule="evenodd"></path>
                        </svg>
                      </span>
                  Invoice
                </button>
                <button
                    type="button"
                    className="col acct_offer_btn4 mx-auto"
                    onClick={() => OpenReportModal()}>
                  Report
                </button>
              </div>
            </div>
          </div>
        </div>
      </>
  )
}