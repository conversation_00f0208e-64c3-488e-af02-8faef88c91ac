import {useQuery} from "react-query";
import {useQueryFix} from "../../vbrae-utils/index.js";
import {getKycUrl} from "../../api/auth/getKycUrl.js";

export const useKycUrl = () => {
    const { data: kycUrl, loading: kycLoading } = useQueryFix({
        query: useQuery({
            queryKey: 'kyc-url',
            queryFn: () => getKycUrl(),
            onError: ()=> undefined,
            refetchOnWindowFocus: false,
        }),
        transform: (response) => response.form_url,
    });

    return { kycUrl, kycLoading };
};