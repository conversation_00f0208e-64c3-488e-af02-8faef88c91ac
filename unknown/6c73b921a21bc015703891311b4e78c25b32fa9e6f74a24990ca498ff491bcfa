/* eslint-disable react/prop-types */
import { createContext, useState } from "react";

// Create the context
export const ModalContext = createContext();

// Provider component to manage global modal state
export const ModalProvider = ({ children }) => {
  const [activeModal, setActiveModal] = useState(null); // Null means no modal is active

  function OpenModal(modal) {
    setActiveModal(modal);
  }
  function CloseModal() {
    setActiveModal(null);
  }

  return (
    <ModalContext.Provider
      value={{
        activeModal,
        OpenModal,
        CloseModal,
      }}>
      {children}
    </ModalContext.Provider>
  );
};
