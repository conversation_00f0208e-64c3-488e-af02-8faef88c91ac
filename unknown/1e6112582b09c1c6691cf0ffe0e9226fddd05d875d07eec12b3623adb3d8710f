import { Link } from "react-router-dom";
import AccountSideBar from "../componets/account/AccountSideBar";
import MainHeader from "../componets/MainHeader";
import MainFooter from "../componets/MainFooter";
import {languageOptions} from "../../constant/languages.js";
import {useCategories} from "../../hooks/categories/useCategories.js";
import {regionsList} from "../../constant/regionList.js";
import useRequestProduct from "../../hooks/categories/useRequestProduct.js";
import Spinner from "../../components/common/Spinner.jsx";

export default function RequestProduct() {

const {categories,categoriesLoading} = useCategories();
const {formik} = useRequestProduct();

  const handleCheckboxChange = (event) => {
    const { checked, value } = event.target;
    if (checked) formik.setFieldValue('language', [...formik.values.language, value]);
    else formik.setFieldValue('language', formik.values.language.filter((lang) => lang !== value));
  };

  const breadCrums = [
    {
      title: "New Offers",
      url: "/account/new-offers",
    },
    {
      title: "Request New Product",
      url: "/account/request-product",
    },
  ];

  const breadCrumsList = breadCrums?.map((item, index) => {
    return (
      <Link to={item.url} key={index}>
        <p className="crumb_link">/ {item.title}</p>
      </Link>
    );
  });

  return (
    <>
      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={'new-offers'} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadCrums}
            activeLink={"new-offers"}
          />

          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <div className="d-lg-none justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-2">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                {breadCrumsList}
              </div>
            </div>
            <div className="acct_cont ">
              <form className="col col-xl-8" onSubmit={formik.handleSubmit}>
                <p className="acct_offer_title mb-4">Request New Product</p>

                <div className="col mb-3">
                  <label htmlFor="title" className="acct_offer_label mb-2">
                    Title*
                  </label>
                  <div className="acct_box">
                    <input
                      type="text"
                      name="title"
                      className="input_box"
                      {...formik.getFieldProps("title")}
                    />
                  </div>
                  {formik.touched.title && <small className="main_footer_copy text-danger">{formik.errors.title}</small>}
                </div>
                <div className="col mb-3">
                  <label htmlFor="steamAppId" className="acct_offer_label mb-2">
                    Steam App ID
                  </label>
                  <div className="acct_box">
                    <input
                      type="text"
                      name="steamAppId"
                      className="input_box"
                      {...formik.getFieldProps("steamAppId")}
                    />
                    {formik.touched.steamAppId && <small className="main_footer_copy text-danger">{formik.errors.steamAppId}</small>}
                  </div>
                </div>
                <div className="col mb-3">
                  <label htmlFor="additionalInformation" className="acct_offer_label mb-2">
                    Additional information (notes)
                  </label>
                  <div className="acct_box">
                    <input
                      type="text"
                      name="additionalInformation"
                      className="input_box"
                      {...formik.getFieldProps("additionalInformation")}
                    />
                  </div>
                </div>

                <div className="col mb-3">
                  <label htmlFor="amount" className="acct_offer_label mb-2">
                    Languages* (audio and subtitles)
                  </label>
                  <div className="col row row-cols-2 row-cols-lg-3 row-cols-xl-4 g-3">
                    {languageOptions.map(item=> (
                        <div className="col d-flex align-items-center gap-2" key={item.id}>
                          <input
                              onChange={handleCheckboxChange}
                              className="acct_offer_check"
                              type="checkbox"
                              value={item.value}
                              name="type"
                              id={item.id}
                          />
                          <label
                              className="acct_offer_text"
                              htmlFor={item.id}>
                            {item.value}
                          </label>
                        </div>
                    ))}
                  </div>
                  {formik.touched.language && <small className="main_footer_copy text-danger">{formik.errors.language}</small>}
                </div>

                <div className="col mb-3">
                  <label htmlFor="region" className="acct_offer_label mb-2">
                    Regional limitation
                  </label>
                  <select className="col-12 acct_sel" id="region" {...formik.getFieldProps("region")}>
                    {regionsList.map(item=> (<option key={item.id} value={item._id}>{item.name}</option>))}
                  </select>
                  {formik.touched.region && <small className="main_footer_copy text-danger">{formik.errors.region}</small>}
                </div>
                <div className="col mb-3">
                  <label htmlFor="category" className="acct_offer_label mb-2">
                    Platform
                  </label>
                  {categoriesLoading ? <div className="d-block">
                    <Spinner />
                  </div> : <select className="col-12 acct_sel" defaultValue="--Choose--"
                                   id="category" {...formik.getFieldProps("category")}>
                    <option value={null}>--Choose--</option>
                    {categories?.map(item => (
                        <option key={item._id} value={item._id}>{item.categoryName.en}</option>
                    ))}
                  </select>}
                  {formik.touched.category && <small className="main_footer_copy text-danger">{formik.errors.category}</small>}
                </div>
                <button disabled={!formik.isValid || formik.isSubmitting} className="col-12 col-md-auto acct_offer_btn1">
                  {formik.isSubmitting ? 'Processing...' : 'Submit'}
                </button>
              </form>
            </div>

            <div className="col d-lg-none mt-4">
              <MainFooter />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
