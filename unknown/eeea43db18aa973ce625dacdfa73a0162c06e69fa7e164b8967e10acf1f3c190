import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getOffer} from "../../api/offers/getOffer.js";

export const useOffer = ({_id}) => {
    const { data: offer, loading: offerLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['offer', _id],
            queryFn: () => getOffer({_id}),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { offer, offerLoading};
};