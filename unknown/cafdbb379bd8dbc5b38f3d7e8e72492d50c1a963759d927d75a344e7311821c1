/* eslint-disable react/prop-types */
import {Link} from "react-router-dom";
import Pagination from "../componets/utility/Pagination";
import AccountSideBar from "../componets/account/AccountSideBar";
import MainHeader from "../componets/MainHeader";
import {useEffect, useState} from "react";
import SetRetailPrice from "../componets/modals/OfferModal";
import MainFooter from "../componets/MainFooter";
import {generateQuery} from "../../vbrae-utils/lib/misc.js";
import {useCategories} from "../../hooks/categories/useCategories.js";
import {useProfile} from "../../hooks/auth/useProfile.js";
import {regionsList} from "../../constant/regionList.js";
import {useSellerOffers} from "../../hooks/offers/useSellerOffers.js";
import useUpdateOffer from "../../hooks/offers/useUpdateOffer.js";
import useDeleteOffer from "../../hooks/offers/useDeleteOffer.js";
import {stockOptions} from "../../constant/stockList.js";
import {useDebounce} from "../../hooks/common/useDebounce.js";
import {getUserId} from "../../vbrae-utils/index.js";
import Spinner from "../../components/common/Spinner.jsx";
import OfferRow from "../../components/offers/OfferRow.jsx";
import MobileOfferRow from "../../components/offers/MobileOfferRow.jsx";

function MyOfferFilter({ isOpen, onClose, setPrices, setSelectedCategory, setSearchState, setSelectedRegion, selectedCategory, selectedRegion, setSelectedStock, selectedStock}) {
  const {categories} = useCategories();

  const [fPrices, setFPrices] = useState([]);
  const [fSearch, setFSearch] = useState('');

  const debouncedSearchTerm = useDebounce(fSearch, 500);
  useEffect(() => {
    setSearchState(debouncedSearchTerm)
  }, [debouncedSearchTerm]);

  const debouncedPriceTerm = useDebounce(fPrices, 500);
  useEffect(() => {
    setPrices(debouncedPriceTerm)
  }, [debouncedPriceTerm]);

  return (
    <>
      <div
        className={
          "col acct_filter_cont flex-column mb-lg-4 " +
          (isOpen ? "d-flex" : "d-none d-lg-flex")
        }>
        <div className="col-auto row g-3 g-lg-2 mb-4 mb-lg-0">
          <p className="col d-flex d-lg-none cate_filter_title align-items-center gap-2 mb-4">
            <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24">
              <g fill="none" fillRule="evenodd">
                <path
                    d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"/>
                <path
                    fill="currentColor"
                    d="M3 4.5A1.5 1.5 0 0 1 4.5 3h15A1.5 1.5 0 0 1 21 4.5v2.086A2 2 0 0 1 20.414 8L15 13.414v7.424a1.1 1.1 0 0 1-1.592.984l-3.717-1.858A1.25 1.25 0 0 1 9 18.846v-5.432L3.586 8A2 2 0 0 1 3 6.586z"
                />
              </g>
            </svg>
            Filter by
            <span
                onClick={onClose}
                className="d-flex justify-content-center align-items-center ms-auto">
              <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24">
                <path
                    fill="currentColor"
                    d="M6.4 19L5 17.6l5.6-5.6L5 6.4L6.4 5l5.6 5.6L17.6 5L19 6.4L13.4 12l5.6 5.6l-1.4 1.4l-5.6-5.6z"></path>
              </svg>
            </span>
          </p>
          <div className="col-xl-6 col-xxl-7 d-none d-lg-block">
            <div className="col acct_box d-flex ">
              <input
                  type="text"
                  className="input_box"
                  placeholder="Search..."
                  value={fSearch}
                  onChange={(e)=> setFSearch(e.target.value)}
              />
              <span className="icon">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 24 24">
                  <path
                      fill="currentColor"
                      fillRule="evenodd"
                      d="M18.319 14.433A8.001 8.001 0 0 0 6.343 3.868a8 8 0 0 0 10.564 11.976l.043.045l4.242 4.243a1 1 0 1 0 1.415-1.415l-4.243-4.242zm-2.076-9.15a6 6 0 1 1-8.485 8.485a6 6 0 0 1 8.485-8.485"
                      clipRule="evenodd"
                  />
                </svg>
              </span>
            </div>
          </div>

          <div className="col-lg-6 col-xl">
            {categories ? <select className="col-12 acct_sel" value={selectedCategory}
                                  onChange={e => setSelectedCategory(e.target.value)}>
              <option value="">Platform</option>
              {categories.map((item, index) => <option key={index} value={item._id}>{item.categoryName.en}</option>)}
            </select> : <div className="d-block">
              <Spinner />
            </div>}
          </div>
          <div className="col-lg-6 col-xl">
            <select className="col-12 acct_sel" value={selectedRegion}
                    onChange={e => setSelectedRegion(e.target.value)}>
              <option value="">Activation region</option>
              {regionsList.map((item, index) => <option key={index} value={item._id}>{item.name}</option>)}
            </select>
          </div>

          {/* Cloumn Brake */}
          <div className="w-100 d-none d-xl-block"></div>

          <div className="col-lg-4 col-xl">
            <select className="col-12 acct_sel ">
              <option value="">Buy Button Visibility</option>
            </select>
          </div>
          <div className="col-lg-8 col-xl-7 d-flex flex-wrap gap-3 gap-lg-2">
            <div className="col-12 col-lg">
              <select className="col-12 acct_sel ">
                <option value="">Smart Pricing</option>
              </select>
            </div>
            <div className="col-12 col-lg">
              <select className="col-12 acct_sel" value={selectedStock} onChange={e => setSelectedStock(e.target.value)}>
                {stockOptions.map(item => <option value={item._id} key={item._id}>{item.name}</option>)}
              </select>
            </div>
          </div>

          <div className="col-lg-6 col-xl d-flex gap-2">
            <div className="col acct_box ">
              <input
                  type="text"
                  className="input_box"
                  placeholder="Min: $0"
                  value={fPrices[0] ?? ""}
                  onChange={e=> setFPrices(prevState => ([e.target.value, prevState[1]]))}/>
            </div>
            <div className="col acct_box">
              <input
                  type="text"
                  className="input_box"
                  placeholder="Max: $100"
                  value={fPrices[1] ?? ""}
                  onChange={e=> setFPrices(prevState => ([prevState[0], e.target.value]))}
              />
            </div>
          </div>
        </div>

        <div className="col-auto d-flex d-lg-none gap-2 cate_filter_btn_con mt-auto">
          <button type="button" className="col cate_filter_btn dark">
            Clear all
          </button>
          <button type="button" className="col cate_filter_btn">
            Apply filter
          </button>
        </div>
      </div>
    </>
  );
}

export default function MyOffers() {

  const userId = getUserId();

  const breadCrums = [
    {
      title: "My Offers",
      url: "/account/offers",
    },
  ];

  const breadCrumsList = breadCrums?.map((item, index) => {
    return (
      <Link to={item.url} key={index}>
        <p className="crumb_link">/ {item.title}</p>
      </Link>
    );
  });

  const {user} = useProfile();
  const [offerToUpdate, setOfferToUpdate] = useState({
    _id: ""
  });
  const [searchState, setSearchState] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedRegion, setSelectedRegion] = useState("");
  const [selectedStock, setSelectedStock] = useState("");
  const [prices, setPrices] = useState([]);
  const [page, setPage] = useState(1);
  const [productToDelete, setProductToDelete] = useState({_id: ""});
  const [activeFilter, setActiveFilter] = useState();

  const {offers, offersLoading} = useSellerOffers({query: generateQuery({
      searchState,
      selectedCategory,
      selectedRegion,
      selectedLanguage:"",
      selectedStock,
      prices,
      page,
      userId:user?._id,
      staticString: `userId=${userId}&limit=12&${activeFilter}`,
    })});
  const {deleteRefetch} = useDeleteOffer(productToDelete);
  const {updateRefetch} = useUpdateOffer(offerToUpdate)

  const {categories} = useCategories();

  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [isRetailPriceOpen, setIsRetailPriceOpen] = useState({value:false, _id: "", expectedPrice:0, customerPays: 0});

  function OpenFilter() {
    setIsFilterOpen(true);
  }
  function CloseFilter() {
    setIsFilterOpen(false);
  }
  function CloseRetailModal() {
    setIsRetailPriceOpen({value:false, _id: "", expectedPrice:0, customerPays: 0});
  }

  const resetFilters = () => {
    setSelectedCategory("");
    setSearchState("");
    setPrices([]);
    setSelectedRegion("");
    setSelectedStock("")
  }

  useEffect(() => {
    if(!offerToUpdate._id) return;

    updateRefetch().finally(()=> setOfferToUpdate({_id: ""}))
  }, [offerToUpdate]);

  useEffect(() => {
    if(!productToDelete._id) return;

    deleteRefetch().finally(()=> setProductToDelete({_id: ""}))
  }, [productToDelete])

  return (
    <>
      <SetRetailPrice
        isOpen={isRetailPriceOpen}
        onClose={() => CloseRetailModal()}
      />

      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={"my-offers"} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadCrums}
            activeLink={"my-offers"}
          />

          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <div className=" d-lg-none justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-2">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                {breadCrumsList}
              </div>
            </div>

            <div className="acct_cont ">
              <div className="col">
                <div className="col row row-cols-2 row-cols-lg-4 g-2 justify-content-between mb-3">
                  <div className="col" onClick={() => setActiveFilter("active=true")}>
                    <div className={`col acct_off_cont ${activeFilter === "active=true" ? "active" : ""}`}>
                      <p className="acct_off_num mb-2">33</p>
                      <p className="acct_off_head">Active</p>
                    </div>
                  </div>
                  <div className="col" onClick={() => setActiveFilter("watchlist=true")}>
                    <div className={`col acct_off_cont ${activeFilter === "watchlist=true" ? "active" : ""}`}>
                      <p className="acct_off_num mb-2">2</p>
                      <p className="acct_off_head">Watchlist</p>
                    </div>
                  </div>
                  <div className="col" onClick={() => setActiveFilter("stock[lte]=0")}>
                    <div className={`col acct_off_cont ${activeFilter === "stock[lte]=0" ? "active" : ""}`}>
                      <p className="acct_off_num mb-2">10</p>
                      <p className="acct_off_head">Out of sttock</p>
                    </div>
                  </div>
                  <div className="col" onClick={() => setActiveFilter("active=false")}>
                    <div className={`col acct_off_cont ${activeFilter === "active=false" ? "active" : ""}`}>
                      <p className="acct_off_num mb-2">20</p>
                      <p className="acct_off_head">Deactivate</p>
                    </div>
                  </div>
                </div>

                <MyOfferFilter
                    isOpen={isFilterOpen}
                    onClose={CloseFilter}
                    setSearchState={setSearchState}
                    selectedCategory={selectedCategory}
                    setSelectedCategory={setSelectedCategory}
                    selectedRegion={selectedRegion}
                    setSelectedRegion={setSelectedRegion}
                    setPrices={setPrices}
                    setSelectedStock={setSelectedStock}
                    selectedStock={selectedStock}
                />

                <div
                    className="col d-xl-flex flex-row-reverse align-items-start align-items-xl-center gap-3 gap-xl-4 mb-4">
                  <div className="col col-xl-3 d-flex gap-2 align-items-center mb-3 mb-xl-0">
                    <p
                        onClick={OpenFilter}
                        className="col d-flex d-lg-none cate_filter_tigger align-items-center gap-2">
                      <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 24 24">
                        <g fill="none" fillRule="evenodd">
                          <path
                              d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"/>
                          <path
                              fill="currentColor"
                              d="M3 4.5A1.5 1.5 0 0 1 4.5 3h15A1.5 1.5 0 0 1 21 4.5v2.086A2 2 0 0 1 20.414 8L15 13.414v7.424a1.1 1.1 0 0 1-1.592.984l-3.717-1.858A1.25 1.25 0 0 1 9 18.846v-5.432L3.586 8A2 2 0 0 1 3 6.586z"
                          />
                        </g>
                      </svg>
                      Filter by
                      <span className="d-flex justify-content-center align-items-center ms-auto">
                        6
                      </span>
                    </p>
                    <select className="col col-lg-6 col-xl cate_filter_sort_sel">
                      <option value="">Most recent</option>
                    </select>
                  </div>

                  <div className="d-lg-none mb-3">
                    <div className="col acct_box d-flex ">
                      <input
                          type="text"
                          className="input_box"
                          placeholder="Search..."
                          value={searchState}
                          onChange={(e)=> setSearchState(e.target.value)}
                      />
                      <span className="icon">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <path
                              fill="currentColor"
                              fillRule="evenodd"
                              d="M18.319 14.433A8.001 8.001 0 0 0 6.343 3.868a8 8 0 0 0 10.564 11.976l.043.045l4.242 4.243a1 1 0 1 0 1.415-1.415l-4.243-4.242zm-2.076-9.15a6 6 0 1 1-8.485 8.485a6 6 0 0 1 8.485-8.485"
                              clipRule="evenodd"
                          />
                        </svg>
                      </span>
                    </div>
                  </div>

                  <div className="col d-flex gap-2 overflow-auto hide_scroll">
                    <span className="cate_filter_tag" onClick={resetFilters}>
                      Clear all
                      <svg
                          className="ms-1"
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>

                    {prices[0] && <span className="cate_filter_tag" onClick={() => setPrices([undefined, prices[1]])}>
                      Min: ${prices[0]}
                      <svg
                          className="ms-1"
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                    {prices[1] && <span className="cate_filter_tag" onClick={() => setPrices([prices[0], undefined])}>
                      Max: ${prices[1]}
                      <svg
                          className="ms-1"
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                    {selectedRegion && <span className="cate_filter_tag" onClick={() => setSelectedRegion("")}>
                      {selectedRegion}
                      <svg
                          className="ms-1"
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                    {selectedCategory && <span className="cate_filter_tag" onClick={() => setSelectedCategory("")}>
                      {categories.find(item => item._id === selectedCategory).categoryName.en}
                      <svg
                          className="ms-1"
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 32 32">
                        <path
                            fill="currentColor"
                            d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                        />
                      </svg>
                    </span>}

                  </div>
                </div>

                <hr className="acct_fil_line mb-0"/>
                {/* Mobile Table */}
                {!offers || offersLoading ? <Spinner /> : <>
                  <div className="col d-lg-none mb-4">
                    {offers.data.map(item => <MobileOfferRow
                        {...item}
                        setOfferToUpdate={setOfferToUpdate}
                        setIsRetailPriceOpen={setIsRetailPriceOpen}
                        setProductToDelete={setProductToDelete}
                        key={item._id}/>)}
                  </div>

                  {/* Desktop Table */}
                  <div className="table-responsive d-none d-lg-block">
                    <table className="w-100 table table-borderless acct_table">
                      <thead>
                      <tr>
                        <th className="acct_table_head"></th>
                        <th className="acct_table_head">Name</th>
                        <th className="acct_table_head d-flex align-items-center gap-1">
                          Stock / Sold
                          <span className="icon">
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                xmlns="http://www.w3.org/2000/svg">
                              <path
                                  d="M3 5V6.66667H7.66667V5H3ZM3 9.16667V10.8333H12.3333V9.16667H3ZM3 13.3333V15H17V13.3333H3Z"/>
                            </svg>
                          </span>
                        </th>
                        <th className="acct_table_head">Sales</th>
                        <th className="acct_table_head">Lowest Price</th>
                        <th className="acct_table_head">Pos</th>
                        <th className="acct_table_head">Achievement</th>
                        <th className="acct_table_head">Sales Booster</th>
                        <th className="acct_table_head">IWTR / Price</th>
                        <th className="acct_table_head"></th>
                      </tr>
                      </thead>
                      <tbody>
                        {offers.data.map(item => <OfferRow
                            {...item}
                            setOfferToUpdate={setOfferToUpdate}
                            setIsRetailPriceOpen={setIsRetailPriceOpen}
                            setProductToDelete={setProductToDelete}
                            key={item._id} />)}
                      </tbody>
                    </table>
                  </div>

                  {!!offers.pagination.pages && <div className="col d-flex justify-content-center">
                    <Pagination
                        totalPages={offers.pagination.pages}
                        currentPage={page}
                        pageClick={(page) => setPage(page)}
                        nextPage={() => setPage(page + 1)}
                        prevPage={() => setPage(page - 1)}/>
                  </div>}
                </>}
              </div>
            </div>

            <div className="col d-lg-none mt-4">
              <MainFooter/>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
