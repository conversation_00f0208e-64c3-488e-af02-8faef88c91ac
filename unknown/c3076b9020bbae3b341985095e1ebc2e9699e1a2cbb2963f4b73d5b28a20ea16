/* eslint-disable react/prop-types */
import useNewsletterForm from "../../../hooks/newsletter/useNewsletterForm";

export default function Subscribe({customClass}) {

  const {formik} = useNewsletterForm();

  return (
    <>
      <div
        className={"main_sub_con " + (customClass ? customClass : "")}
        style={{
          backgroundImage: "url(./assets/images/sub_bg.png)",
        }}>
        <div className="main_sub_cont d-lg-flex align-items-center gap-3 ">
          <div className="col">
            <h4 className="main_sub_title">Get 15% off your first purchase!</h4>
            <p className="main_sub_text">
              Sign up for our newsletter and receive special offers
            </p>
          </div>
          <form onSubmit={formik.handleSubmit} className="col d-flex gap-3 mt-3 mt-lg-0">
              <input
                type="email"
                className="main_input"
                placeholder="E-mail address"
                {...formik.getFieldProps('email')}
              />
              <button type="submit" className="main_button" disabled={formik.isSubmitting || !formik.isValid}>
                  {formik.isSubmitting ? "Processing..." : "Subscribe"}
              </button>
          </form>
        </div>
      </div>
    </>
  );
}
