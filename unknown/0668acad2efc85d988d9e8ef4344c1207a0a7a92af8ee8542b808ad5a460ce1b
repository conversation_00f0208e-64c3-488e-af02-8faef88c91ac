import PropTypes from "prop-types";

export default function Pagination({
                                     totalPages = 1,
                                     currentPage = 1,
                                     pageClick = () => {},
                                     nextPage = () => {},
                                     prevPage = () => {},
                                   }) {
  // Helper function to generate page numbers
  const generatePages = (currentPage, totalPages) => {
    const pages = [];
    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage > 3) {
        pages.push(1, '...');
      }
      for (
          let i = Math.max(1, currentPage - 2);
          i <= Math.min(totalPages, currentPage + 2);
          i++
      ) {
        pages.push(i);
      }
      if (currentPage < totalPages - 2) {
        pages.push('...', totalPages);
      }
    }
    return pages;
  };

  const pages = generatePages(currentPage, totalPages);

  return (
      <div className="d-flex gap-1">
        {/* Previous Button */}
        <p
            className={`pagi_item arrow d-flex justify-content-center align-items-center me-2 ${
                currentPage === 1 ? "disabled" : ""
            }`}
            onClick={() => currentPage > 1 && prevPage(currentPage - 1)}
        >
          <svg
              xmlns="http://www.w3.org/2000/svg"
              width="1em"
              height="1em"
              viewBox="0 0 24 24"
          >
            <path
                fill="currentColor"
                d="M19.2 2.43L16.778 0L4.8 12l11.978 12l2.422-2.43L9.653 12z"
            />
          </svg>
        </p>

        {/* Page Numbers */}
        {pages.map((page, index) =>
            page === "..." ? (
                <p key={index} className="pagi_item dots">
                  {page}
                </p>
            ) : (
                <p
                    key={index}
                    className={`pagi_item d-flex justify-content-center align-items-center ${
                        currentPage === page ? "active" : ""
                    }`}
                    onClick={() => pageClick(page)}
                >
                  {page}
                </p>
            )
        )}

        {/* Next Button */}
        <p
            className={`pagi_item arrow d-flex justify-content-center align-items-center ms-2 ${
                currentPage === totalPages ? "disabled" : ""
            }`}
            onClick={() => currentPage < totalPages && nextPage(currentPage + 1)}
        >
          <svg
              xmlns="http://www.w3.org/2000/svg"
              width="1em"
              height="1em"
              viewBox="0 0 24 24"
          >
            <path
                fill="currentColor"
                d="M4.8 21.57L7.222 24L19.2 12L7.222 0L4.8 2.43L14.347 12z"
            />
          </svg>
        </p>
      </div>
  );
}

// Prop Types Validation
Pagination.propTypes = {
  totalPages: PropTypes.number.isRequired,
  currentPage: PropTypes.number.isRequired,
  pageClick: PropTypes.func.isRequired,
  nextPage: PropTypes.func.isRequired,
  prevPage: PropTypes.func.isRequired,
};

// Default Props
Pagination.defaultProps = {
  totalPages: 1,
  currentPage: 1,
  pageClick: () => {},
  nextPage: () => {},
  prevPage: () => {},
};
