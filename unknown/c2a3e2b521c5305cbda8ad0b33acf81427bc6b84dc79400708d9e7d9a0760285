import {useQuery, useQueryClient} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {patchCart} from "../../api/cart/patchCart.js";

export const usePatchCart = (props) => {

    const queryClient = useQueryClient();

    const { loading: patchLoading, refetch: patchRefetch } = useQueryFix({
        query: useQuery({
            queryKey: ['patch-cart', props.offerId],
            queryFn: () => patchCart(props),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: false,
            onSuccess: ()=> queryClient.invalidateQueries(['get-cart']).finally(),
        }),
        transform: (data) => data,
    });

    return { patchLoading, patchRefetch };
};