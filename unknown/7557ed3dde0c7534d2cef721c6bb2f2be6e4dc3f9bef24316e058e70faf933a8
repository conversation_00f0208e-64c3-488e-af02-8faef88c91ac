import {useQuery, useQueryClient} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {deleteNotification} from "../../api/notifications/deleteNotification.js";


export default function useDeleteNotification({_id}) {
    const queryClient = useQueryClient();
    const { refetch: deleteRefetch, loading: deleteLoading } = useQueryFix({
        query: useQuery({
            queryKey: "",
            queryFn: () => deleteNotification({_id}),
            onError: showError,
            onSuccess: () => queryClient.invalidateQueries(['notifications']).finally(),
            enabled: false,
        }),
        transform: (data) => data,
    });
    return { deleteRefetch, deleteLoading };
}