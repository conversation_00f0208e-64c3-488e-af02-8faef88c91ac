import {useQuery} from "react-query";
import {getAccessToken, showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getCart} from "../../api/cart/getCart.js";

export const useCart = () => {
    const hasAccessToken = getAccessToken();
    const { data: cartData, loading: cartLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['get-cart'],
            queryFn: () => getCart(),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: !!hasAccessToken
        }),
        transform: (data) => data.data,
    });

    return { cartData, cartLoading };
};