import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getAllBanners} from "../../api/banners/getAllBanners.js";
import bannerTransformer from "./transformer/bannerTransformer.js";

export const useBanners = () => {
    const { data: banners, loading: bannerLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['banners'],
            queryFn: () => getAllBanners(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => bannerTransformer(data.data.banners),
    });

    return { banners, bannerLoading };
};