import {getOrders} from "../../api/orders/getOrders.js";

import {useQuery} from "react-query";
import {getAccessToken, showError, useQueryFix} from "../../vbrae-utils/index.js";

export const useOrders = () => {

    const { data: orders, loading: orderLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['orders'],
            queryFn: () => getOrders(),
            onError: showError,
            enabled: !!getAccessToken(),
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data.orders,
    });

    return { orders, orderLoading};
};