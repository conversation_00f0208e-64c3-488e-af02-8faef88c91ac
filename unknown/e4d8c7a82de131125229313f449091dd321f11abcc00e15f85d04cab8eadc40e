import {Outlet, useNavigate} from "react-router-dom";
import {getAccessToken} from "../../vbrae-utils/index.js";
import {useContext, useEffect} from "react";
import {ModalContext} from "../../store/ModalContext.jsx";

export default function ProtectedLayout() {

    const { OpenModal } = useContext(ModalContext);
    const navigate = useNavigate();
    const hasUser = !!getAccessToken()

    useEffect(() => {
        if(!hasUser){
            OpenModal('login')
            navigate("/")
        };
    }, [hasUser]);

    return (
        <>
            <Outlet />
        </>
    );
}
