import {useContext} from "react";
import {SideBarContext} from "../../store/SideBarContext";
import {Link} from "react-router-dom";
import LatestPurchase from "../../components/latestPurchase.jsx";
import {useProfile} from "../../hooks/auth/useProfile.js";
import {useBlogs} from "../../hooks/blogs/useBlogs.js";
import {convertToDays} from "../../vbrae-utils/lib/time.js";

export default function RightSideBar() {

  const {user} = useProfile();
  const {blogs} = useBlogs({query: ''});

  const { isRightSideMenuCollaped, ToggleRightSideMenu } =
    useContext(SideBarContext);

  if(!user || !blogs) return;

  return (
    <>
      <div
        className={
          "right_sidebar_con d-flex flex-column sticky-top position-relative " +
          (isRightSideMenuCollaped ? "open" : "close")
        }>
        {isRightSideMenuCollaped ? (
          <span
            onClick={() => ToggleRightSideMenu()}
            className="rs_close d-flex justify-content-center align-items-center position-absolute"
            role="button">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="1em"
              height="1em"
              viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M4.8 21.57L7.222 24L19.2 12L7.222 0L4.8 2.43L14.347 12z"
              />
            </svg>
          </span>
        ) : (
          <span
            onClick={() => ToggleRightSideMenu()}
            className="rs_close open d-flex justify-content-center align-items-center position-absolute"
            role="button">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="1em"
              height="1em"
              viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M19.2 2.43L16.778 0L4.8 12l11.978 12l2.422-2.43L9.653 12z"></path>
            </svg>
          </span>
        )}

        <div className="rec_con">
          <div className="rec_cont position-relative">
            <img
              src={`${window.origin}/assets/images/game_pad.png`}
              alt=""
              className="rec_img position-absolute"
            />
            <p className="rec_title">Get 10%</p>
            <div className="col p-3">
              <p className="rec_text">
                Recommend Products <br /> and <span>earn money</span>
              </p>
            </div>
          </div>
        </div>
        <div className="right_sidebar_cont">
          <div className="col mb-4">
            <p className="rs_header">Latest Purchase</p>
            <LatestPurchase/>
          </div>
          <div className="col">
          <p className="rs_header">LATEST ARTICLES</p>
            {blogs.data.map((blog, index) => (
                <div className="col" key={index}>
                  <Link to={"/blog-details"} className="">
                    <div
                        className="col rs_article_item mb-3"
                        style={{
                          backgroundImage: "url(./assets/images/article_bg.png)",
                        }}>
                      <div className="rs_article_item_details">
                        <div className="d-flex justify-content-between align-items-center mb-2">
                          <span className="rs_article_item_type">{blog.keywords[0]}</span>
                          <span className="rs_article_item_time">{convertToDays(blog.createdAt)}</span>
                        </div>
                        <p className="rs_article_item_title">
                          {blog.title}
                        </p>
                      </div>
                    </div>
                  </Link>
                </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
}
