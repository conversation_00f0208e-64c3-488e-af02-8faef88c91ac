import {useQuery, useQueryClient} from "react-query";
import {useQueryFix} from "../../vbrae-utils/index.js";
import {patchNotifications} from "../../api/notifications/patchNotifications.js";

export const useNotificationState = () => {

    const queryClient = useQueryClient();

    useQueryFix({
        query: useQuery({
            queryKey: ['notification-state'],
            queryFn: () => patchNotifications(),
            refetchOnWindowFocus: false,
            onSuccess: ()=> queryClient.invalidateQueries(["notifications"])
        }),
        transform: (data) => data,
    });
};