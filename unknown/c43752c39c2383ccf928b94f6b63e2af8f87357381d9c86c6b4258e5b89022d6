import {useQuery, useQueryClient} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {deleteOffer} from "../../api/offers/deleteOffer.js";


export default function useDeleteOffer(props) {
  const queryClient = useQueryClient();
  const { refetch: deleteRefetch, loading: deleteLoading } = useQueryFix({
    query: useQuery({
      queryKey: [''],
      queryFn: () => deleteOffer(props),
      onError: showError,
      onSuccess: () => queryClient.invalidateQueries(['game-item']).finally(),
      enabled: false,
    }),
    transform: (data) => data,
  });
  return { deleteRefetch, deleteLoading };
}