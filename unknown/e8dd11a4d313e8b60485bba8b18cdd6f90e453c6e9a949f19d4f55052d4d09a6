import Swal from "sweetalert2";

export const showCongrats = (message, handleSuccess) => {
    Swal.fire({
        title: '🎉 Congratulations',
        html: `
      <img 
        src="/assets/images/party.webp" 
        alt="Celebration" 
        style="width: 120px; border-radius: 50%; margin-bottom: 20px;" 
      />
      <p style="color: #ffffff; font-size: 16px; margin-top: 10px;">
         ${message}
      </p>
    `,
        showConfirmButton: true,
        background: "#171E2E",
        confirmButtonText: 'Copy Coupon',
        confirmButtonColor: '#212121',
        color:"white",
        customClass: {
            title: 'swal2-title-custom',
            htmlContainer: 'swal2-html-custom',
            confirmButton: 'swal2-confirm-custom',
        },
    }).then((result) => {
        if (result.isConfirmed && typeof handleSuccess === 'function') {
            handleSuccess();
        }
    });
};
