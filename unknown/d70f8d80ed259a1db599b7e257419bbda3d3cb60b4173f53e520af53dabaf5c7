import {useQuery, useQueryClient} from "react-query";
import {useQueryFix} from "../../vbrae-utils/index.js";
import {patchUser} from "../../api/auth/patchUser.js";

export const usePatchUser = (addressState) => {

    const queryClient = useQueryClient();
    const { loading: patchLoading, refetch:patchRefetch } = useQueryFix({
        query: useQuery({
            queryKey: ['patch-profile', addressState],
            queryFn: () => patchUser(addressState),
            enabled: false,
            onSuccess: ()=> queryClient.invalidateQueries('user-profile').finally(),
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { patchLoading, patchRefetch};
};