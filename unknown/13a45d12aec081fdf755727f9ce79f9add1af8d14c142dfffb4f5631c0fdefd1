import {Link, useSearchParams} from "react-router-dom";
import MenuSideBar from "./componets/MenuSideBar";
import MainHeader from "./componets/MainHeader";
import {useEffect, useState} from "react";
import MainFooter from "./componets/MainFooter";
import OrderDetails from "./componets/checkout/OrderDetails.jsx";
import {useCart} from "../hooks/cart/useCart.js";
import BillingAddress from "./componets/checkout/BillingAddress.jsx";
import PaymentButton from "./componets/checkout/PaymentButton.jsx";
import Spinner from "../components/common/Spinner.jsx";
import {usePostOrder} from "../hooks/cart/usePostOrder.js";
import {showError} from "../vbrae-utils/index.js";
import {useCountryTax} from "../hooks/tax/useCountryTax.js";

export default function CheckOut() {

  const [selectedMethod, setSelectedMethod] = useState("swedbank");
  const [searchParams] = useSearchParams();
  const { countryTax } = useCountryTax();
  const [showServiceFeeTooltip, setShowServiceFeeTooltip] = useState(false);

  const status = searchParams.get("status");

  const { orderRefetch } = usePostOrder();
  const { cartData, cartLoading } = useCart();

  function ToggleServiceFeeTooltip() {
    setShowServiceFeeTooltip(!showServiceFeeTooltip);
  }
  function CloseServiceFeeTooltip() {
    setShowServiceFeeTooltip(false);
  }

  useEffect(() => {
    if(status === 'success') orderRefetch().finally();
    else if(status === "failed") showError({message: "Payment Failed!"});
  }, [status]);

  const taxAmount = countryTax
      ? (cartData.summary.totalActualPrice * countryTax) / 100
      : 0;

  return (
    <>
      <div className="d-flex main_house_con">
        <MenuSideBar makeShort={true} activeLink={'checkout'} />

        <div
          className="col housing_con d-flex flex-column">
          <MainHeader activeLink={'checkout'} />

          <div className="housing d-flex gap-1 position-relative">
            <div id="scrollable-section" className="col main_section">
              <div className="d-flex align-items-center gap-2 mb-3">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                <Link to={"/cart"}>
                  <p className="crumb_link">/ Cart</p>
                </Link>
                <Link>
                  <p className="crumb_link">/ Checkout</p>
                </Link>
              </div>
              {cartLoading || !cartData ? <Spinner /> :
              <div className="main_cont">
                <div className="col d-xl-flex gap-4">
                  <div className="col d-flex gap-3 gap-xl-4 align-items-center overflow-auto mb-4">
                    <Link to={"/cart"}>
                      <p className="cart_tab done">
                        Cart {cartLoading ? <Spinner /> :
                          <span>({cartData.items.length} items)</span>}
                      </p>
                    </Link>
                    <hr className="cart_tab_line" />
                    <Link to={"/checkout"}>
                      <p className="cart_tab active">Checkout</p>
                    </Link>
                    <hr className="cart_tab_line" />
                    <Link to={"/"}>
                      <p className="cart_tab">Your placed</p>
                    </Link>
                  </div>
                  {/* For An Empty Importance Space */}
                  <div className="col-3"></div>
                </div>

                <div className="col d-xl-flex gap-4 mb-5">
                  {/* Address and Payment */}
                  <div className="col mb-4 mb-xl-0">
                    {/* Addresses */}
                    <BillingAddress />

                    {/* Payment */}
                    <div className="col cart_item_con mb-4">
                      <p className={`cart_pay_hint d-flex gap-2 align-items-center`}>
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 24 24">
                          <path
                            fill="currentColor"
                            d="m11.005 2l7.298 2.28a1 1 0 0 1 .702.955V7h2a1 1 0 0 1 1 1v2h-13V8a1 1 0 0 1 1-1h7V5.97l-6-1.876l-6 1.876v7.404a4 4 0 0 0 1.558 3.169l.189.136l4.253 2.9L14.787 17h-4.782a1 1 0 0 1-1-1v-4h13v4a1 1 0 0 1-1 1l-3.22.001c-.387.51-.857.96-1.4 1.33L11.005 22l-5.38-3.668a6 6 0 0 1-2.62-4.958V5.235a1 1 0 0 1 .702-.954z"
                          />
                        </svg>
                        All transactions are secured, processed and authorized
                        by external payment providers
                      </p>

                      <hr className="cart_item_line" />

                      <div className="col">
                        <div className={`${selectedMethod === "swedbank" ? "active" : ""} col cart_pay_item d-flex gap-2 gap-md-4 justify-content-between align-items-center`}>
                          <input
                            type="radio"
                            className="cart_pay_radio"
                            name="method"
                            value="swedbank"
                            checked={selectedMethod === "swedbank"}
                            onChange={() => setSelectedMethod("swedbank")}
                          />
                          <div className="col-5 col-md-3 col-xl-2 d-flex gap-2 justify-content-center">
                            <img
                              src={"./assets/images/icons/swed.svg"}
                              alt=""
                              className="cart_pay_img"
                            />
                          </div>
                          <div className="col">
                            <p className="cart_pay_head mb-1">
                              Credit or debit card
                            </p>
                            <p className="cart_pay_text d-none d-md-block">
                              Pay via Visa, Mastercard, Maestro, American
                              Express, or Discover debit or credit card
                            </p>
                          </div>
                        </div>

                        <hr className="cart_item_line" />

                        <div className={`col cart_pay_item d-flex gap-4 justify-content-between align-items-center ${selectedMethod === "crypto" ? "active" : ""}`}>
                          <input
                            type="radio"
                            className="cart_pay_radio"
                            name="method"
                            value="crypto"
                            checked={selectedMethod === "crypto"}
                            onChange={() => setSelectedMethod("crypto")}
                          />
                          <div className="col-5 col-md-3 col-xl-2 d-flex gap-1 justify-content-center">
                            <img
                              src={"./assets/images/icons/btc.svg"}
                              alt=""
                              className="cart_pay_img"
                            />
                            <img
                              src={"./assets/images/icons/btc.svg"}
                              alt=""
                              className="cart_pay_img"
                            />
                            <img
                              src={"./assets/images/icons/btc.svg"}
                              alt=""
                              className="cart_pay_img"
                            />
                            <img
                              src={"./assets/images/icons/btc.svg"}
                              alt=""
                              className="cart_pay_img"
                            />
                          </div>
                          <div className="col">
                            <p className="cart_pay_head mb-1">
                              Crypto currencies
                            </p>
                            <p className="cart_pay_text d-none d-md-block">
                              Pay with your selected crypto currency
                            </p>
                          </div>
                          <div className="col-auto d-none d-lg-block">
                            <p className="cart_pay_pow">powered by</p>
                            <img
                              src={"./assets/images/cry-logo.png"}
                              alt=""
                              className="cart_pay_pow_img"
                            />
                          </div>
                        </div>
                      </div>
                    </div>

                    <Link to={"/cart"}>
                      <p className="cart_item_shop d-flex align-items-center gap-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 24 24">
                          <path
                            fill="currentColor"
                            d="M19.2 2.43L16.778 0L4.8 12l11.978 12l2.422-2.43L9.653 12z"
                          />
                        </svg>
                        Back to cart
                      </p>
                    </Link>
                  </div>
                  {/* Order Summary */}
                  <div className="col col-xl-3">
                    <div className="col cart_sum_con mb-3">
                      <p className="cart_sum_head mb-4">Order summary</p>

                      <p className="cart_sum_text d-flex justify-content-between align-items-center mb-2">
                        Products <span className="white">${cartData.summary.totalActualPrice}</span>
                      </p>
                      <p className="cart_sum_text d-flex justify-content-between align-items-center position-relative mb-2">
                        Service fee
                        <span
                          onClick={ToggleServiceFeeTooltip}
                          className="icon ms-1 me-auto"
                          role="button">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                            <path
                              fill="currentColor"
                              fillRule="evenodd"
                              d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10M12 6.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V7a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2"
                              clipRule="evenodd"
                            />
                          </svg>
                        </span>
                        <span>{new Intl.NumberFormat('en-Us', {
                          currency: 'USD',
                          style: 'currency',
                        }).format(cartData.summary.serviceFee)}</span>
                        {/* Tooltip */}
                        {showServiceFeeTooltip && (
                          <div className="cart_sum_tip position-absolute position-relative d-flex gap-2 ">
                            {/* Close Icon */}
                            <span
                              onClick={CloseServiceFeeTooltip}
                              className="cart_sum_tip_close position-absolute"
                              role="button">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 32 32">
                                <path
                                  fill="currentColor"
                                  d="M16 2C8.2 2 2 8.2 2 16s6.2 14 14 14s14-6.2 14-14S23.8 2 16 2m5.4 21L16 17.6L10.6 23L9 21.4l5.4-5.4L9 10.6L10.6 9l5.4 5.4L21.4 9l1.6 1.6l-5.4 5.4l5.4 5.4z"
                                />
                              </svg>
                            </span>

                            <span className="cart_sum_tip_icon">
                              <svg
                                className="me-auto"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                                <path
                                  fill="currentColor"
                                  fillRule="evenodd"
                                  d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10M12 6.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V7a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2"
                                  clipRule="evenodd"
                                />
                              </svg>
                            </span>
                            <div className="col">
                              <p className="mb-0">
                                Fee covers the costs of delivering top quality
                                services to our users, including anti-fraud
                                protection, 24/7 customer support, merchants
                                verification, compliance measures etc. It allows
                                us to ensure that we can always deliver a wide
                                selection of products from merchants all over
                                the world at fair prices. See Terms & Conditions
                                for more details.
                              </p>
                            </div>
                          </div>
                        )}
                      </p>
                      <p className="cart_sum_text d-flex justify-content-between align-items-center mb-2">
                        Tax <span className="">{new Intl.NumberFormat("en-US", {
                        style: "currency",
                        currency: "USD",
                      }).format(taxAmount)}</span>
                      </p>
                      <p className="cart_sum_text d-flex justify-content-between align-items-center mb-4">
                        Savings <span className="green">${cartData.summary.totalSavings}</span>
                      </p>

                      <p className="cart_sum_head d-flex justify-content-between align-items-center ">
                        Total <span>{new Intl.NumberFormat('en-Us', {
                          currency: 'USD',
                          style: 'currency',
                      }).format(cartData.summary.totalCustomerPays + taxAmount)}</span>
                      </p>
                    </div>

                    {/* Order Details */}
                    <OrderDetails />

                    <div className="cart_sum_con mb-4">
                      <p className="cart_ord_head d-flex gap-2 align-items-center mb-3">
                        <span className="">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                            <path
                              fill="currentColor"
                              fillRule="evenodd"
                              d="M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12s4.477 10 10 10s10-4.477 10-10M12 6.25a.75.75 0 0 1 .75.75v6a.75.75 0 0 1-1.5 0V7a.75.75 0 0 1 .75-.75M12 17a1 1 0 1 0 0-2a1 1 0 0 0 0 2"
                              clipRule="evenodd"
                            />
                          </svg>
                        </span>
                        Your local tax
                      </p>

                      <div className="col">
                        <select className="cart_sum_sel" disabled={true} defaultValue={countryTax ? "inside-eu" : "outside-eu"}>
                          <option value="ouside-eu">Outside EU</option>
                          <option value="inside-eu">Inside EU</option>
                        </select>
                      </div>
                    </div>

                    <div className="col text-center">
                      {cartData && <PaymentButton amount={cartData.summary.totalCustomerPays + taxAmount} method={selectedMethod} />}

                      <p className="col-12 col-md-10 cart_ord_hint text-start mx-auto">
                        By clicking this button I declare that I have read the{" "}
                        <Link className="link">
                          <u>Terms & Conditions</u>
                        </Link>
                        and the
                        <Link className="link">
                          <u>Privacy Policy</u>
                        </Link>
                        and agree to the terms.
                      </p>
                    </div>
                  </div>
                </div>
              </div>}

              <div className="col d-lg-none">
                <MainFooter />
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
