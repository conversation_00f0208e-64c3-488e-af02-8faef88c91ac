import {Link} from "react-router-dom";
import AccountSideBar from "../componets/account/AccountSideBar";
import MainHeader from "../componets/MainHeader";
import {useTabs} from "../../services/CustomTabs";
import ProfileDetails from "../../components/profile/ProfileDetails.jsx";
import ShopSetting from "../../components/profile/ShopSetting.jsx";
import DualAuth from "../../components/profile/DualAuth.jsx";
import ProfileLinks from "../../components/profile/ProfileLinks.jsx";
import ProfileAddress from "../../components/profile/ProfileAddress.jsx";
import useShopSettingForm from "../../hooks/shop/useShopSettingForm.js";
import {useMyShops} from "../../hooks/shop/useMyShops.js";
import Spinner from "../../components/common/Spinner.jsx";
import {useProfile} from "../../hooks/auth/useProfile.js";

export default function Profile() {
  const breadCrums = [
    {
      title: "Settings",
      url: "/account/profile",
    },
  ];

  const breadCrumsList = breadCrums?.map((item, index) => {
    return (
      <Link to={item.url} key={index}>
        <p className="crumb_link">/ {item.title}</p>
      </Link>
    );
  });

  return (
    <>
      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={'settings'} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadCrums}
            activeLink={"settings"}
          />

          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <div className=" d-lg-none justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-2">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                {breadCrumsList}
              </div>
            </div>

            <RenderProfile />
          </div>
        </div>
      </div>
    </>
  );
}


const RenderProfile = () => {

  const {user} = useProfile();

  const { activeTab, ChangeTab } = useTabs(1);
  const {shop} = useMyShops({user});
  const {formik} = useShopSettingForm({existingShop: shop});

  if(!user) return <Spinner />

  return (
      <form className="acct_cont" onSubmit={formik.handleSubmit}>
        <div className="col d-flex flex-wrap justify-content-between gap-4 align-items-center mb-4">
          <p className="d-none d-xl-block acct_offer_title">Settings</p>

          <div
              className="col hide_scroll d-flex gap-2 justify-content-md-center align-items-center overflow-auto">
            <p
                className={"dash_tab_link " + (activeTab == 1 && "active")}
                onClick={() => ChangeTab(1)}>
              Account Details
            </p>
            {user.role === "seller" && <>
              <p
                  className={"dash_tab_link " + (activeTab == 2 && "active")}
                  onClick={() => ChangeTab(2)}>
                Shop Settings
              </p>
              <p
                  className={"dash_tab_link " + (activeTab == 3 && "active")}
                  onClick={() => ChangeTab(3)}>
                2FA
              </p>
              <p
                  className={"dash_tab_link " + (activeTab == 4 && "active")}
                  onClick={() => ChangeTab(4)}>
                LINKS
              </p>
              <p
                  className={"dash_tab_link " + (activeTab == 5 && "active")}
                  onClick={() => ChangeTab(5)}>
                Address
              </p>
            </>}
          </div>

          <div className="col-auto offer_save_btn_con d-flex justify-content-center">
            <button type="submit" className="col acct_offer_btn" disabled={formik.isSubmitting}>
              {formik.isSubmitting ? "Processing..." : "Save"}
            </button>
          </div>
        </div>

        {/* Account Details */}
        <div>
          {activeTab == 1 && <ProfileDetails formik={formik} avatar={user.avatar} coverImage={user.coverImage}/>}

          {user.role === "seller" && <>
            {/* Shop Settings */}
            {activeTab == 2 && <ShopSetting formik={formik}/>}

            {/* Two Factor Authentication */}
            {activeTab == 3 && <DualAuth formik={formik}/>}

            {/* Links */}
            {activeTab == 4 && <ProfileLinks formik={formik}/>}

            {/* Addresses */}
            {activeTab == 5 && <ProfileAddress formik={formik}/>}
          </>}
        </div>

        {/* Important Empty Space */}
        <div className="col d-lg-none my-5 "></div>
      </form>
  )
}