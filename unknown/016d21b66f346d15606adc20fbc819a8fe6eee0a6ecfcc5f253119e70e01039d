import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getTicketDetails} from "../../api/ticket/getTicketDetails.js";

export const useTicketDetails = ({_id}) => {
    const { data: ticketDetails, loading: ticketDetailsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['ticket', _id],
            queryFn: () => getTicketDetails({_id}),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: !!_id
        }),
        transform: (data) => data.data,
    });

    return { ticketDetails, ticketDetailsLoading};
};