import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {updateOffer} from "../../api/offers/updateOffer.js";
import {useEffect, useState} from "react";

const priceSchema = Yup.object().shape({
    expectedPrice:Yup.number().required('Price is required'),
    customerPays: Yup.number().required('Price is required'),
});

export default function useRetailPrice({_id, expectedPrice, customerPays, onClose}) {

    const queryClient = useQueryClient();

    const [initialValues, setInitialState] = useState({
        expectedPrice: 1,
        customerPays: 1,
    })


    useEffect(() => {
        if(!_id) return;

        setInitialState({expectedPrice, customerPays});
    }, [_id, expectedPrice, customerPays]);

    const { mutateAsync } = useMutation(updateOffer, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: priceSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync({...values, _id});
            setSubmitting(false);
            resetForm();
            if (response) {
                queryClient.invalidateQueries(["seller-offers"])
                showSuccess("Prices updated successfully.");
                onClose();
            }
        },
    });

    return { formik };
}
