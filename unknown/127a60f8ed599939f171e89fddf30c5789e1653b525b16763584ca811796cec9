import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getBlogDetails} from "../../api/blog/getBlogDetails.js";
import {useParams} from "react-router-dom";

export const useBlogDetails = () => {

    const {id= ''} = useParams();

    const { data: details, loading: detailsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['blog-details', id],
            queryFn: () => getBlogDetails({_id: id}),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { details, detailsLoading};
};