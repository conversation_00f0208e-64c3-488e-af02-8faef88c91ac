import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {postLoginForm} from "../../api/auth/postLoginForm.js";
import {setAccessToken, showError} from "../../vbrae-utils/index.js";
import {useContext} from "react";
import {ModalContext} from "../../store/ModalContext.jsx";

const loginSchema = Yup.object().shape({
    email:Yup.string().trim()
        .email('Wrong email format')
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Email is required'),
    password: Yup.string()
        .min(6, 'Minimum 6 characters')
        .max(50, 'Maximum 50 characters')
        .required('Password is required'),
});

export default function useLoginForm() {
    const { CloseModal } = useContext(ModalContext);
    const queryClient = useQueryClient();
    const initialValues = {
        email: '',
        password: '',
    }

    const { mutateAsync } = useMutation(postLoginForm, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: loginSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync(values);
            setSubmitting(false);
            resetForm();
            if (response) {
                setAccessToken(response.token, response.user._id, response.clearSaleToken);
                queryClient.invalidateQueries(['user-profile']).finally()
                CloseModal()
            }
        },
    });

    return { formik };
}
