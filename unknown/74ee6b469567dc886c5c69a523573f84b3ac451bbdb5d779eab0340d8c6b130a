import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getNewOffers} from "../../api/offers/getNewOffers.js";

export const useNewOffers = ({query}) => {
    const { data: offers, loading: offersLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['new-offers', query],
            queryFn: () => getNewOffers({query}),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { offers, offersLoading};
};