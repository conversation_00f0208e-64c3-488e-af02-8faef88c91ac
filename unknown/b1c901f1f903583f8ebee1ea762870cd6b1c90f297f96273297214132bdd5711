import {useContext} from "react";
import {ModalContext} from "../store/ModalContext";
import ForgotPasswordForm from "../components/auth/forgot-password-form.jsx";

export default function ForgotPasswordModal() {
    const { activeModal, CloseModal } = useContext(ModalContext); //Modal Global State

    if (activeModal !== "forgot") return null;
    return (
        <>
            <div className="modal_con d-lg-flex justify-content-center align-items-center">
                <div className="col col-lg-10 col-xl-6 register_con">
                    <div
                        className="col d-none d-lg-block login_img"
                        style={{
                            backgroundImage: "url(./assets/images/login_img.png)",
                        }}></div>
                    <div className="col register_cont position-relative">
            <span
                onClick={() => CloseModal()}
                className="register_close position-absolute">
              <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="1em"
                  height="1em"
                  viewBox="0 0 24 24">
                <path
                    fill="currentColor"
                    d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"
                />
              </svg>
            </span>
                        <p className="register_header">Welcome back</p>
                        <ForgotPasswordForm/>
                    </div>
                </div>
            </div>
        </>
    );
}
