import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getConversations} from "../../api/conversation/getConversations.js";

export const useConversations = () => {

    const { data: conversations, loading: conversationLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['conversations'],
            queryFn: () => getConversations(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { conversations, conversationLoading};
};