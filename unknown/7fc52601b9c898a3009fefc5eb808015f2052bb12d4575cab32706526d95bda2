import * as Yup from 'yup';
import {useMutation} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import { postNewsletter } from '../../api/newsletter/postNewsletter.js';
import {showCongrats} from "../../vbrae-utils/lib/formCongrats.js";

const schema = Yup.object().shape({
    email:Yup.string().trim()
        .email('Wrong email format')
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Email is required')
});

export default function useNewsletterForm() {
    const initialValues = {
        email: ''
    }

    const { mutateAsync } = useMutation(postNewsletter, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: schema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const geoResponse = await fetch('https://geolocation-db.com/json/');
            const data = await geoResponse.json();
            const response = await mutateAsync({...values, country: data.country_name});
            setSubmitting(false);
            resetForm();
            if (response) {
                showCongrats(
                    `You have successfully unlocked your special coupon code: <span style="font-weight: 600; color: #1095ED">${response.coupon.code}</span>. Use it at checkout to enjoy your exclusive discount!`,
                    ()=> navigator.clipboard.writeText(response.coupon.code)
                        .then(() => {
                            console.log(`Coupon code "${response.coupon.code}" copied to clipboard!`);
                        })
                        .catch((err) => {
                            console.error('Failed to copy the coupon code: ', err);
                        }))
            }
        },
    });

    return { formik };
}
