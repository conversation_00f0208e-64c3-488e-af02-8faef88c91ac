import {Link} from "react-router-dom";
import AccountSideBar from "../componets/account/AccountSideBar";
import MainHeader from "../componets/MainHeader";
import {useState} from "react";
import {useTabs} from "../../services/CustomTabs";
import MainFooter from "../componets/MainFooter";
import AllConversations from "../../components/conversations/AllConversations.jsx";
import ConversationBody from "../../components/conversations/ConversationBody.jsx";

export default function Messages() {
  const [breadcrumbs] = useState([
    {
      title: "Messages",
      url: "/account/messages",
    }
  ]);

  const breadCrumbsList = breadcrumbs?.map((item, index) => {
    return (
      <Link to={item.url} key={index}>
        <p className="crumb_link">/ {item.title}</p>
      </Link>
    );
  });

  return (
    <>
      <div
        className="d-flex w-100 main_house_con"
        style={{
          background: "none",
        }}>
        <AccountSideBar activeLink={"messages"} />

        <div className="col housing_con d-flex flex-column">
          <MainHeader
            showHeader={true}
            isAccount={true}
            breadCrums={breadcrumbs}
            activeLink={"messages"}
          />

          <div id="scrollable-section" className="col acct_cont_con pt-lg-0">
            <div className=" d-lg-none justify-content-between align-items-center mb-3">
              <div className="d-flex align-items-center gap-2">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                {breadCrumbsList}
              </div>
            </div>

            <div className="acct_cont ">
              <RenderChats />
            </div>

            <div className="col d-lg-none mt-4">
              <MainFooter />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}


function RenderChats(){

  const [isSelected, setIsSelected] = useState(false);

  const { activeTab, ChangeTab } = useTabs(1);
  function ToggleSelected() {
    setIsSelected(!isSelected);
  }

  return (
      <>
        <div className="d-xl-flex align-items-start gap-3">
          <div
              className={
                  "col-12 col-xl-4 d-flex flex-column " +
                  (isSelected ? "d-none d-xl-flex" : "d-flex")
              }>
            <div className="d-flex gap-2 mb-4">
              <p
                  className={
                      "dash_tab_link " + (activeTab == 1 && "active")
                  }
                  onClick={() => ChangeTab(1)}>
                All (33)
              </p>
              <p
                  className={
                      "dash_tab_link " + (activeTab == 2 && "active")
                  }
                  onClick={() => ChangeTab(2)}>
                Archive
              </p>
            </div>

            <div className="col msg_main_con mb-4">
              <AllConversations ToggleSelected={ToggleSelected} />
            </div>
          </div>
          <div
              className={
                  "col msg_dea_cont " +
                  (isSelected ? "d-block" : "d-none d-xl-block")
              }>
            <ConversationBody ToggleSelected={ToggleSelected} />
          </div>
        </div>
      </>
  )
}