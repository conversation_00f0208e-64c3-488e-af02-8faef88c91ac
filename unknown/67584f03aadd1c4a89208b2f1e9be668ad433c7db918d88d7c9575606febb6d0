import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {useContext} from "react";
import {ModalContext} from "../../store/ModalContext.jsx";
import {patchUser} from "../../api/auth/patchUser.js";

const addressSchema = Yup.object().shape({
    street: Yup.string()
        .trim()
        .required('Street is required'),
    city: Yup.string()
        .trim()
        .required('City is required'),
    state: Yup.string()
        .trim()
        .required('State is required'),
    zip: Yup.string()
        .trim()
        .required('ZIP code is required'),
    country: Yup.string()
        .trim()
        .required('Country is required'),
});

export default function useBillingForm({existingAddress}) {
    const queryClient = useQueryClient();
    const { CloseModal } = useContext(ModalContext);
    const initialValues = {
        street: '',
        city: '',
        state: '',
        zip: '',
        country: '',
    }

    const { mutateAsync } = useMutation(patchUser, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: addressSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync({address:[...existingAddress,values]});
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess("Address Added.");
                await queryClient.invalidateQueries("user-profile");
                await queryClient.invalidateQueries("payment");
                CloseModal()
            }
        },
    });

    return { formik };
}
