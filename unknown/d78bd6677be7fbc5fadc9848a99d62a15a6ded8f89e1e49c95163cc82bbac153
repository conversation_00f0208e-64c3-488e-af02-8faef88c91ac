import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getSales} from "../../api/sales/getSales.js";
import saleTransformer from "./transformer/saleTransformer.js";

export const useSales = ({query, id}) => {
    const { data: salesData, loading: salesLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['get-sales', query],
            queryFn: () => getSales({query}),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => saleTransformer(data, id),
    });

    return { salesData, salesLoading };
};