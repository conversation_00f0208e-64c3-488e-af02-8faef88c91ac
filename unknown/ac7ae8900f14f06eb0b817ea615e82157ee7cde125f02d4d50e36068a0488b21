import {useQuery, useQueryClient} from "react-query";
import {getUserId, showError, useQueryFix} from "../../vbrae-utils/index.js";
import {postFollow} from "../../api/shop/postFollow.js";
import {useParams} from "react-router-dom";

export const useFollow = () => {

    const queryClient = useQueryClient();
    const userId = getUserId();
    const {id} = useParams();

    const { loading: followLoading, refetch:followRefetch } = useQueryFix({
        query: useQuery({
            queryKey: ['follow', userId, id],
            queryFn: () => postFollow({userId, shopId: id}),
            onError: showError,
            onSuccess: ()=> queryClient.invalidateQueries(['shop', id]),
            enabled: false,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { followLoading, followRefetch};
};