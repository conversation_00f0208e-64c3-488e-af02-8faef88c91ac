import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getOfferDetails} from "../../api/offers/getOfferDetails.js";

export const useOfferDetails = ({_id}) => {
    const { data: offer, loading: offerLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['offer-get', _id],
            queryFn: () => getOfferDetails({_id}),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: !!_id
        }),
        transform: (data) => data,
    });

    return { offer, offerLoading};
};