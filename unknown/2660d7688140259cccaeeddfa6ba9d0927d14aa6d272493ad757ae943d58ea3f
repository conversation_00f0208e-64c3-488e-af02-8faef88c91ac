import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getLevels} from "../../api/levels/getLevels.js";

export const useLevels = () => {
    const { data: levels, loading: levelsLoading } = useQueryFix({
        query: useQuery({
            queryKey: 'levels',
            queryFn: () => getLevels(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { levels, levelsLoading};
};