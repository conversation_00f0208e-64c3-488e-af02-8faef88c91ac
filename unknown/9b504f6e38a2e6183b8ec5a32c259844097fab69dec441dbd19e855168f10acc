import {ModalProvider} from "../../store/ModalContext";
import MainFooter from "../componets/MainFooter";
import {Outlet} from "react-router-dom";

import RegisterModal from "../Register";
import LoginModal from "../Login";
import CookieModal from "../componets/Cookies";
import ScrollToTop from "../componets/utility/ScrollToTop";
import ForgotPasswordModal from "../ForgotPassword.jsx";
import ResetPasswordModal from "../ResetPasswordModal.jsx";
import AddressModal from "../componets/modals/AddressModal.jsx";
import ShopModal from "../componets/modals/ShopModal.jsx";

export default function MainLayout() {

  return (
      <div className="main_wrapper d-flex flex-column">
        <ModalProvider>
          <Outlet/>

          {/* General Modals */}
          {<RegisterModal/>}
          {<LoginModal/>}
          {<ResetPasswordModal/>}
          {<ForgotPasswordModal/>}
          {<CookieModal/>}
          {<AddressModal/>}
          {<ShopModal/>}
        </ModalProvider>

        <ScrollToTop/>

        <div className="col d-none d-lg-block">
          <MainFooter/>
        </div>
      </div>
  );
}
