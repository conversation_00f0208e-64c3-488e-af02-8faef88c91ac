/* eslint-disable react/prop-types */

import {useDashboard} from "../../../hooks/dashboard/useDashboard.js";
import Spinner from "../../../components/common/Spinner.jsx";
import {useLevels} from "../../../hooks/levels/useLevels.js";

export default function DashboardLevel({ isOpen, onClose }) {

  const {dashboard, dashboardLoading} = useDashboard({year: new Date().getFullYear()});
  const {levels, levelsLoading} = useLevels();

  if (dashboardLoading || levelsLoading) return <Spinner />;

  const {orders} = dashboard;

  return (
    <>
      {isOpen && (
        <div className="modal_con d-flex justify-content-center align-items-start align-items-lg-center inset-0">
          <div className="col col-lg-11 col-xl-8 dash_level position-relative">
            <span
              className="dash_level_close position-absolute"
              onClick={() => onClose()}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"
                />
              </svg>
            </span>
            <p className="dash_level_title text-lg-center mb-4">Level matrix</p>

            {/* Mobile Table */}
            <div className="col d-lg-none mb-4">
              {levels.map((item, idx) => (
                  <div
                      key={idx}
                      className={`mob_table_con position-relative mb-3 ${item.isYourLevel ? "active" : ""}`}
                  >
                    <div className="col row gx-4 gy-3">
                      <div className="col-6">
                        <label className={`mob_table_head ${item.isYourLevel ? "blue" : ""}`}>Level</label>
                        <p className="mob_table_data">{item.name}</p>
                      </div>
                      <div className="col-6">
                        <label className="mob_table_head">№ of Sales</label>
                        <p className="mob_table_data">
                          {item.fromOrders} - {item.toOrders !== null ? item.toOrders : '∞'}
                        </p>
                      </div>
                      <div className="col-6">
                        <label className="mob_table_head">Earnings Release Days</label>
                        <p className="mob_table_data bold">{item.earningReleaseDays}</p>
                      </div>
                      <div className="col-6">
                        <label className="mob_table_head">Commission</label>
                        <p className="mob_table_data">{item.commissionPercent}%</p>
                      </div>
                    </div>
                  </div>
              ))}
            </div>


            {/* Desktop Table */}
            <div className="col d-none d-lg-flex gap-2">
              {[
                { title: "Level", key: "name", bg: "./assets/images/dash_bg4.png" },
                { title: "№ of Sales", key: "sales", bg: "./assets/images/dash_bg2.png" },
                { title: "Earnings Release Days", key: "earningReleaseDays", bg: "./assets/images/dash_bg1.png" },
                { title: "Commission", key: "commissionPercent", bg: "./assets/images/dash_bg3.png" },
              ].map((column, index) => (
                  <div
                      key={index}
                      className="col d-flex flex-column dash_level_cont text-center"
                      style={{ backgroundImage: `url(${column.bg})` }}
                  >
                    <p className="col dash_level_head mb-3">{column.title}</p>
                    {levels.map((item, idx) => {
                      const isYourLevel =
                          orders.sellerRevenue >= item.fromOrders &&
                          orders.sellerRevenue <= item.toOrders;

                      const value =
                          column.key === "sales"
                              ? `${item.fromOrders} - ${item.toOrders !== null ? item.toOrders : '∞'}`
                              : column.key === "commissionPercent"
                                  ? `${item[column.key]}%`
                                  : item[column.key];

                      const isActive = isYourLevel && column.key === "name";
                      const isActive2 = isYourLevel && (column.key === "sales" || column.key === "earningReleaseDays");
                      const isActive3 = isYourLevel && column.key === "commissionPercent";

                      return (
                          <p
                              key={idx}
                              className={`col dash_level_text ${
                                  isActive ? "active1 d-flex justify-content-center position-relative" :
                                      isActive2 ? "active2" :
                                          isActive3 ? "active3" : ""
                              }`}
                          >
                            {isActive ? (
                                <>
                <span className="dash_level_text_sm position-absolute">
                  your <br /> level
                </span>
                                  <span>{value}</span>
                                </>
                            ) : (
                                value
                            )}
                          </p>
                      );
                    })}
                  </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
