import { Link } from "react-router-dom";
import {convertToDays} from "../../../vbrae-utils/lib/time.js";
import {truncateText} from "../../../vbrae-utils/lib/misc.js";

/* eslint-disable react/prop-types */
export default function ArticleItem({ customClass, title, createdAt, summary, keywords, _id}) {
  return (
    <>
      <Link to={`/blog-details/${_id}`}>
        <div
          className={
            "article_item " +
            (customClass ? customClass : "col-10 col-md-5 col-lg-4 col-xl-3 ")
          }
          style={{
            backgroundImage: "url(./assets/images/article_bg.png)",
          }}>
          <div className="article_item_details">
            <div className="d-flex justify-content-between align-items-center mb-2">
              <span className="article_item_type">{keywords[0]}</span>
              <span className="article_item_time">{convertToDays(createdAt)}</span>
            </div>
            <p className="article_item_title">{truncateText(title, 33)}</p>
            <p className="article_item_text">{summary}</p>
          </div>
        </div>
      </Link>
    </>
  );
}
