import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getCategories} from "../../api/categories/getCategories.js";

export const useCategories = () => {
    const { data: categories, loading: categoriesLoading } = useQueryFix({
        query: useQuery({
            queryKey: 'categories',
            queryFn: () => getCategories(),
            onError: showError,
            staleTime: 10000,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { categories, categoriesLoading};
};