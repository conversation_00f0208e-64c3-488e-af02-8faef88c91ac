import * as Yup from 'yup';
import {useMutation} from "react-query";
import {useFormik} from "formik";
import {getUserId, showError, showSuccess} from "../../vbrae-utils/index.js";
import {useContext} from "react";
import {ModalContext} from "../../store/ModalContext.jsx";
import {postShop} from "../../api/shop/postShop.js";

const shopSchema = Yup.object().shape({
    shopName: Yup.string()
        .trim()
        .required('Shop Name is required'),
    firstName: Yup.string()
        .trim()
        .required('First Name is required'),
    lastName: Yup.string()
        .trim()
        .required('Last Name is required'),
    phoneNumber: Yup.string()
        .trim()
        .required('Phone Number is required'),
    location: Yup.string(),
    shopDescription: Yup.string()
        .trim()
        .required('Description is required'),
});

export default function useShopForm({tier}) {
    const { CloseModal } = useContext(ModalContext);
    const initialValues = {
        shopName: '',
        firstName: '',
        lastName: '',
        phoneNumber: '',
        location: '',
        shopDescription:'',
    }

    const { mutateAsync } = useMutation(postShop, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: shopSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync({...values, seller: getUserId(), tier});
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess("Request submitted successfully.");
                CloseModal("shop")
            }
        },
    });

    return { formik };
}
