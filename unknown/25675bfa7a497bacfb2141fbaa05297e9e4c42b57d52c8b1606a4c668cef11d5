import {showError} from "./formErrors.js";

export const handleLoginSuccess = async (response) => {
    return await fetch("https://www.googleapis.com/oauth2/v1/userinfo?alt=json", {
        method: "GET",
        headers: {
            Authorization: `Bearer ${response.access_token}`,
            Accept: "application/json",
        },
    }).then(r => r.json())
};

export const handleLoginError = (error) => {
    showError(error);
};

export const handleFacebookCallback = (response) => {
    if (response?.status === "unknown") {
        showError({error: 'Something went wrong with facebook Login.'});
    }
    return response;
}