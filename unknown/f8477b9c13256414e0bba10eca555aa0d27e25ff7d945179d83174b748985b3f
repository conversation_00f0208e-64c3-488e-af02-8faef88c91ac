import {Link, useParams, useSearchParams} from "react-router-dom";
import MenuSideBar from "./componets/MenuSideBar";
import MainHeader from "./componets/MainHeader";
import {useState} from "react";
import {useTabs} from "../services/CustomTabs";
import RightSideBar from "./componets/RightSideBar";
import MainFooter from "./componets/MainFooter";
import ShopHero from "../components/shop-details/ShopHero.jsx";
import {useShopDetails} from "../hooks/shop/useShopDetails.js";
import Spinner from "../components/common/Spinner.jsx";
import MessageModal from "./componets/modals/ProductMessage.jsx";
import SellerOffers from "../components/shop-details/SellerOffers.jsx";
import BestSellerOffers from "../components/shop-details/BestSellerOffers.jsx";
import ProductReviews from "../components/product-details/productReviews.jsx";
import {useReviews} from "../hooks/rating/useReviews.js";

export default function ShopDetails() {
  const [openMessage, setOpenMessage] = useState(false);
  return (
    <>
      {/*<MessageModal isOpen={openMessage} onClose={()=>setOpenMessage(false)} />*/}
      <div className="d-flex main_house_con">
        <MenuSideBar makeShort={true} activeLink={"shops"} />

        <div
          className="col housing_con d-flex flex-column"
          style={
            {
              // backgroundImage: " url('./assets/images/psn_bg.png')",
            }
          }>
          <MainHeader activeLink={"shops"} />

          <div className="housing d-flex gap-1 position-relative">
            <div id="scrollable-section" className="col main_section">
              <div className="d-flex align-items-center gap-2 mb-3">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                <Link to={"/shops"}>
                  <p className="crumb_link">/ Shops</p>
                </Link>
                <Link>
                  <p className="crumb_link">/ Details</p>
                </Link>
              </div>

              <RenderShop setOpenMessage={setOpenMessage}/>

              <div className="col d-lg-none">
                <MainFooter />
              </div>
            </div>

            <RightSideBar />
          </div>
        </div>
      </div>
    </>
  );
}

const RenderShop = ({setOpenMessage}) => {

  const {id = ''} = useParams();

  const [shopCounts, setShopCounts] = useState({
    offers: 0,
    bestOffers: 0,
    reviews: 0,
  });

  const [searchParams] = useSearchParams();
  const {shopDetails,shopDetailsLoading} = useShopDetails({_id: id});
  const {reviews, reviewLoading} = useReviews({userId: searchParams.get("seller")});
  const { activeTab, ChangeTab } = useTabs(1);

  if(!shopDetails || shopDetailsLoading || reviewLoading) return <Spinner />;

  return (
      <>
        <div className="shop_details_con">
          <ShopHero {...shopDetails} setOpenMessage={setOpenMessage}/>
          <div className="col d-flex justify-content-center mb-2">
            <div
                className="col col-xl-5 shop_tab_con d-flex  justify-content-md-center align-items-center overflow-auto hide_scroll">
              <p
                  className={
                      "shop_tab_link " + (activeTab == 1 ? "active" : "")
                  }
                  onClick={() => ChangeTab(1)}>
                Offers ({shopCounts.offers})
              </p>
              <p
                  className={
                      "shop_tab_link " + (activeTab == 2 ? "active" : "")
                  }
                  onClick={() => ChangeTab(2)}>
                Reviews ({reviews.ratings.length})
              </p>
              <p
                  className={
                      "shop_tab_link " + (activeTab == 3 ? "active" : "")
                  }
                  onClick={() => ChangeTab(3)}>
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="1em"
                    height="1em"
                    viewBox="0 0 256 256">
                  <path
                      fill="currentColor"
                      d="M143.38 17.85a8 8 0 0 0-12.63 3.41l-22 60.41l-24.16-23.41a8 8 0 0 0-11.93.89C51 87.53 40 116.08 40 144a88 88 0 0 0 176 0c0-59.45-50.79-108-72.62-126.15m40.51 135.49a57.6 57.6 0 0 1-46.56 46.55a7.7 7.7 0 0 1-1.33.11a8 8 0 0 1-1.32-15.89c16.57-2.79 30.63-16.85 33.44-33.45a8 8 0 0 1 15.78 2.68Z"
                  />
                </svg>
                Best Sellers {shopCounts.bestOffers > 0 && `(${shopCounts.bestOffers})`}
              </p>
            </div>
          </div>
          <div className="shop_details_cont">
            {/* Offers */}
            {activeTab == 1 && <SellerOffers sellerId={shopDetails.seller._id} setShopCounts={setShopCounts}/>}

            {/* Reviews */}

            {activeTab == 2  && <ProductReviews {...reviews}/>}

            {/* Best Sellers */}
            {activeTab == 3 && <BestSellerOffers sellerId={shopDetails.seller._id} setShopCounts={setShopCounts}/>}
          </div>
        </div>
      </>
  )
}