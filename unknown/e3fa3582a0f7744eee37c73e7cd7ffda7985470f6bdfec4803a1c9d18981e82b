import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getBlogs} from "../../api/blog/getBlogs.js";

export const useBlogs = (props) => {
    const { data: blogs, loading: blogsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['blog', {...props}],
            queryFn: () => getBlogs(props),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { blogs, blogsLoading};
};