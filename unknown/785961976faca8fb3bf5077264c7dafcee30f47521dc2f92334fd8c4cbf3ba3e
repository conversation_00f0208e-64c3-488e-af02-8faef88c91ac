import {useQuery, useQueryClient} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {postWishlist} from "../../api/wishlist/postWishlist.js";

export const usePostWishlist = ({offerId}) => {
    const queryClient = useQueryClient();
    const { loading: offersLoading, refetch:offerRefetch, isFetching } = useQueryFix({
        query: useQuery({
            queryKey: ['post-wishlist', offerId],
            queryFn: () => postWishlist({offerId}),
            onError: showError,
            refetchOnWindowFocus: false,
            enabled: false,
            onSuccess: ()=> {
                queryClient.invalidateQueries("game-item").finally();
            }
        }),
        transform: (data) => data,
    });

    return { offerRefetch, offersLoading, isFetching};
};