import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {useParams} from "react-router-dom";
import {updateOffer} from "../../api/offers/updateOffer.js";
import {useEffect, useState} from "react";
import {connectListing} from "../../vbrae-utils/lib/misc.js";

const offerSchema = Yup.object().shape({
    expectedPrice: Yup.string().required('Expected Price is required'),
    customerPays: Yup.string().required('Customer Price is required'),
    instantDelivery: Yup.boolean(),
    licenseKeys: Yup.string(),
    delivery: Yup.string(),
});

export default function useEditOffer({offer, step}) {

    const {id} = useParams();
    const queryClient = useQueryClient();

    const [initialValues, setInitialValues] = useState({
        expectedPrice: "",
        customerPays: "",
        instantDelivery: true,
        licenseKeys: '',
        stock: 0,
        deliveryTime: "24"
    })

    useEffect(() => {
        if (!offer) return;

        setInitialValues({
            expectedPrice: offer.expectedPrice,
            customerPays: offer.customerPays,
            instantDelivery: offer.instantDelivery,
            licenseKeys: offer.licenseKeys.length > 0 ? offer.licenseKeys.join('\n') : "",
            stock: offer.stock,
            deliveryTime: offer.deliveryTime
        })
    }, [offer]);

    const {mutateAsync} = useMutation(updateOffer, {
        onError: (error) => showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: offerSchema,
        onSubmit: async (values, {setSubmitting}) => {
            let requestBody
            if (step === "step-1") {
                requestBody = {
                    expectedPrice: values.expectedPrice,
                    customerPays: values.customerPays,
                    instantDelivery: values.instantDelivery,
                }
            }
            if (step === "step-2") {
                requestBody = {
                    instantDelivery: values.instantDelivery,
                    licenseKeys: connectListing(values.licenseKeys),
                    stock: connectListing(values.licenseKeys).length || values.stock,
                    deliveryTime: values.deliveryTime,
                }
            }
            setSubmitting(true);
            const response = await mutateAsync({...requestBody, _id: id});
            setSubmitting(false);
            if (response) {
                queryClient.invalidateQueries(['offer-get', id])
                showSuccess("Offer updated updated");
            }
        },
    });

    return {formik};
}