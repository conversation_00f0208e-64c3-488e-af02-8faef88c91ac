import {useQuery} from "react-query";
import {showError, showSuccess, useQueryFix} from "../../vbrae-utils/index.js";
import {postPlaceOrder} from "../../api/clearSale/postPlaceOrder.js";

export const usePlaceOrder = (payload) => {

    console.log(payload);

    const { data: placeOrder, loading: placeOrderLoading } = useQueryFix({
        query: useQuery({
            queryKey: 'place-order',
            queryFn: () => postPlaceOrder(payload),
            onError: showError,
            onSuccess: ()=>showSuccess("Order is under review!"),
            refetchOnWindowFocus: false,
            enabled: !!payload.Orders,
        }),
        transform: (response) => response.data,
    });

    return { placeOrder, placeOrderLoading};
};