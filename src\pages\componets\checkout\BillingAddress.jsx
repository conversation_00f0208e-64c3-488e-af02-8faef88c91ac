import {useContext, useEffect, useState} from "react";
import {ModalContext} from "../../../store/ModalContext.jsx";
import AddressCard from "./AddressCard.jsx";
import {useProfile} from "../../../hooks/auth/useProfile.js";
import {showError} from "../../../vbrae-utils/index.js";
import {usePatchUser} from "../../../hooks/auth/usePatchUser.js";
import Spinner from "../../../components/common/Spinner.jsx";

export default function BillingAddress(){

    const { OpenModal } = useContext(ModalContext);

    const [addressState, setAddressState] = useState(null);

    const {user, userLoading} = useProfile();
    const {patchLoading, patchRefetch} = usePatchUser({address:addressState})

    const handleRemoveAddress = (_id) => {
        if(!user) return showError("User not found!");

        setAddressState(user.address.filter(item=> item._id !== _id));
    }

    useEffect(() => {
        if (!user || !addressState) return;

        patchRefetch().finally()
    }, [addressState]);

    return (
        <div className="col mb-4">
            <p className="cart_accr_head d-flex gap-3 align-items-center mb-3">
                        <span>
                          <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 24 24">
                            <path
                                fill="currentColor"
                                d="M21.57 19.2L24 16.778L12 4.8L0 16.778L2.43 19.2L12 9.653z"
                            />
                          </svg>
                        </span>
                Billing Address
            </p>

            <div className="col d-flex flex-wrap gap-3">
                {(userLoading || !user || patchLoading) ? <Spinner /> : <>
                    {user.address.map((address) => <AddressCard key={address._id} {...address} userName={user.name} onRemove={handleRemoveAddress}/>)}
                </>}
                <div className="col-12 col-md cart_add_item new d-flex justify-content-center align-items-center">
                    <p className="cart_add_new d-flex gap-2 align-items-center" onClick={() => OpenModal('address')}>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="20"
                            height="20"
                            viewBox="0 0 24 24">
                            <path
                                fill="currentColor"
                                d="M12 2C6.486 2 2 6.486 2 12s4.486 10 10 10s10-4.486 10-10S17.514 2 12 2m5 11h-4v4h-2v-4H7v-2h4V7h2v4h4z"></path>
                        </svg>
                        Add new address
                    </p>
                </div>
            </div>
        </div>
    )
}