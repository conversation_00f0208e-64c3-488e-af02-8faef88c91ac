import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getHomeConfigs} from "../../api/home/<USER>";

export const useHomeConfigs = () => {
    const { data: settings, loading: settingsLoading } = useQueryFix({
        query: useQuery({
            queryKey: 'settings',
            queryFn: () => getHomeConfigs(),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { settings, settingsLoading};
};