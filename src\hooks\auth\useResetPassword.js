import * as Yup from 'yup';
import {useMutation} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {postResetForm} from "../../api/auth/postResetForm.js";
import {useSearchParams} from "react-router-dom";
import {useContext} from "react";
import {ModalContext} from "../../store/ModalContext.jsx";

const resetSchema = Yup.object().shape({
    password:Yup.string().trim()
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Password is required'),
    confirmPassword:Yup.string().trim()
        .min(3, 'Minimum 3 characters')
        .max(50, 'Maximum 50 characters')
        .required('Confirm Password is required'),
});

export default function useResetPassword() {
    const { CloseModal } = useContext(ModalContext);
    const [searchParams, setSearchParams] = useSearchParams();
    const initialValues = {
        password: '',
        confirmPassword: ''
    }

    const { mutateAsync } = useMutation(postResetForm, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: resetSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            const token = searchParams.get("token");
            setSubmitting(true);
            const response = await mutateAsync({...values, token});
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess(response.message);
                setSearchParams("");
                CloseModal()
            }
        },
    });

    return { formik };
}
