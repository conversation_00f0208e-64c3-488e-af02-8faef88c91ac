import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getSellerOffers} from "../../api/offers/getSellerOffers.js";

export const useSoftware = ({query}) => {
    const { data: softwareOffers, loading: softwareOfferLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['game-item','software', query],
            queryFn: () => getSellerOffers({query}),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data.data,
    });

    return { softwareOffers, softwareOfferLoading};
};