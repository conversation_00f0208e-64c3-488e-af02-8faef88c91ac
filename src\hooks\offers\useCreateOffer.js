import * as Yup from 'yup';
import {useMutation} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {createOffer} from "../../api/offers/createOffer.js";
import {useParams} from "react-router-dom";
import {connectListing} from "../../vbrae-utils/lib/misc.js";

const offerSchema = Yup.object().shape({
    expectedPrice: Yup.string().required('Expected Price is required'),
    customerPays: Yup.string().required('Customer Price is required'),
    instantDelivery: Yup.boolean(),
    licenseKeys: Yup.string(),
    active: Yup.boolean(),
    stock: Yup.number(),
    deliveryTime: Yup.string(),
});

export default function useCreateOffer({onChangeSteps, ...props}) {

    const {id} = useParams();

    const initialValues = {
        expectedPrice: "",
        customerPays: "",
        instantDelivery: true,
        licenseKeys: '',
        active: true,
        stock: 0,
        deliveryTime: "24"
    }

    const { mutateAsync } = useMutation(createOffer, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: offerSchema,
        onSubmit: async (values, { setSubmitting }) => {
            setSubmitting(true);
            const response= await mutateAsync({
                ...props,
                ...values,
                template: id,
                stock: connectListing(values.licenseKeys).length || values.stock,
                licenseKeys: connectListing(values.licenseKeys)
            });
            setSubmitting(false);
            if (response) {
                showSuccess("Offer successfully created");
                onChangeSteps(response.data._id);
            }
        },
    });

    return { formik };
}

