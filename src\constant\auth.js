export const passwordKeys = [
    {
        id: "defaultCheck1",
        label: "at least one uppercase character",
        key: "hasUppercase"
    },
    {
        id: "defaultCheck2",
        label: "at least one lowercase character",
        key: "hasLowercase"
    },
    {
        id: "defaultCheck3",
        label: "at least one special character",
        key: "hasSpecialChar"
    },
    {
        id: "defaultCheck4",
        label: "at least one number",
        key: "hasNumber"
    },
    {
        id: "defaultCheck5",
        label: "at least 8 character",
        key: "minLength"
    },
    {
        id: "defaultCheck6",
        label: "maximum 38 character",
        key: "maxLength"
    }
]

export const passwordRequirements = (password) => {
    const requirements = {
        minLength: password.length >= 8,
        maxLength: password.length > 0 && password.length <= 38,
        hasUppercase: /[A-Z]/.test(password),
        hasLowercase: /[a-z]/.test(password),
        hasNumber: /\d/.test(password),
        hasSpecialChar: /[!@#$%^&*(),.?":{}|<>]/.test(password),
    };

    return {
        minLength: requirements.minLength,
        maxLength: requirements.maxLength,
        hasUppercase: requirements.hasUppercase,
        hasLowercase: requirements.hasLowercase,
        hasNumber: requirements.hasNumber,
        hasSpecialChar: requirements.hasSpecialChar,
    };
};