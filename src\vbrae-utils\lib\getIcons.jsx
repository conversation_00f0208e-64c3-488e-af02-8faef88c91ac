export const getIcon = (slug) => {
    switch (slug) {
        case 'psn':
            return (
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20px"
                    height="20px"
                    viewBox="0 0 24 24"
                >
                    <path
                        fill="currentColor"
                        d="M8.984 2.596v17.547l3.915 1.261V6.688c0-.69.304-1.151.794-.991c.636.18.76.814.76 1.505v5.875c2.441 1.193 4.362-.002 4.362-3.152c0-3.237-1.126-4.675-4.438-5.827c-1.307-.448-3.728-1.186-5.39-1.502zm4.656 16.241l6.296-2.275c.715-.258.826-.625.246-.818c-.586-.192-1.637-.139-2.357.123l-4.205 1.5V14.98l.24-.085s1.201-.42 2.913-.615c1.696-.18 3.785.03 5.437.661c1.848.601 2.04 1.472 1.576 2.072c-.465.6-1.622 1.036-1.622 1.036l-8.544 3.107V18.86zM1.807 18.6c-1.9-.545-2.214-1.668-1.352-2.32c.801-.586 2.16-1.052 2.16-1.052l5.615-2.013v2.313L4.205 17c-.705.271-.825.632-.239.826c.586.195 1.637.15 2.343-.12L8.247 17v2.074c-.12.03-.256.044-.39.073c-1.939.331-3.996.196-6.038-.479z"
                    />
                </svg>
            );
        case "xbox":
            return (
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20px"
                    height="20px"
                    viewBox="0 0 24 24"
                >
                    <path
                        fill="currentColor"
                        d = "M5.42 19.528A9.96 9.96 0 0 0 12 22a9.97 9.97 0 0 0 6.585-2.472c1.564-1.594-3.597-7.258-6.585-9.515c-2.985 2.257-8.15 7.921-6.582 9.515m9.3-12.005c2.083 2.467 6.236 8.594 5.063 10.76A9.95 9.95 0 0 0 22 12.002a9.96 9.96 0 0 0-2.975-7.113s-.023-.018-.068-.035a.7.7 0 0 0-.234-.038c-.494 0-1.655.362-4.005 2.706M5.045 4.855c-.048.017-.068.034-.072.035A9.96 9.96 0 0 0 2 12.003c0 2.379.832 4.561 2.217 6.278C3.051 16.11 7.201 9.988 9.285 7.523C6.935 5.178 5.772 4.818 5.28 4.818a.6.6 0 0 0-.234.039zM12 4.959S9.546 3.523 7.63 3.455c-.753-.027-1.213.246-1.268.282C8.15 2.539 10.05 2 11.988 2H12c1.945 0 3.838.538 5.638 1.737c-.056-.038-.512-.31-1.266-.282c-1.917.068-4.372 1.5-4.372 1.5z"
                    />
                </svg>
            );
        case "nintendo":
            return (
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20px"
                    height="20px"
                    viewBox="0 0 24 24"
                >
                    <path
                        fill="currentColor"
                        d = "M14.176 24h3.674c3.376 0 6.15-2.774 6.15-6.15V6.15C24 2.775 21.226 0 17.85 0H14.1c-.074 0-.15.074-.15.15v23.7c-.001.076.075.15.226.15m4.574-13.199c1.351 0 2.399 1.125 2.399 2.398c0 1.352-1.125 2.4-2.399 2.4c-1.35 0-2.4-1.049-2.4-2.4c-.075-1.349 1.05-2.398 2.4-2.398M11.4 0H6.15C2.775 0 0 2.775 0 6.15v11.7C0 21.226 2.775 24 6.15 24h5.25c.074 0 .15-.074.15-.149V.15c.001-.076-.075-.15-.15-.15M9.676 22.051H6.15a4.194 4.194 0 0 1-4.201-4.201V6.15A4.194 4.194 0 0 1 6.15 1.949H9.6zM3.75 7.199c0 1.275.975 2.25 2.25 2.25s2.25-.975 2.25-2.25c0-1.273-.975-2.25-2.25-2.25s-2.25.977-2.25 2.25"
                    />
                </svg>
            );
        case "pc-gaming":
            return (
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20px"
                    height="20px"
                    viewBox="0 0 24 24"
                >
                    <g fill="none">
                        <path
                            d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"
                        />
                        <path
                            fill="currentColor"
                            d="M21 13v7.434a1.5 1.5 0 0 1-1.553 1.499l-.133-.011L12 21.008V13zm-11 0v7.758l-5.248-.656A2 2 0 0 1 3 18.117V13zm9.314-10.922a1.5 1.5 0 0 1 1.68 1.355l.006.133V11h-9V2.992zM10 3.242V11H3V5.883a2 2 0 0 1 1.752-1.985z"
                        />
                    </g>
                </svg>
            );
        case "gift-cards":
            return (
                < svg
                    width = "20px"
                    height = "20px"
                    viewBox = "0 0 24 24"
                    fill = "currentColor"
                    xmlns = "http://www.w3.org/2000/svg" >
                    < path
                        fillRule = "evenodd"
                        clipRule = "evenodd"
                        d = "M5.36507 1.05753C3.95596 1.36627 3.00399 2.79413 3.30667 4.14488C3.34272 4.30587 3.3869 4.47303 3.40479 4.5164C3.43498 4.58958 3.38023 4.59521 2.63643 4.59521C2.19596 4.59521 1.72366 4.61799 1.58681 4.64582C1.31085 4.70202 0.950563 4.86329 0.712864 5.03702C0.475305 5.21067 0.19662 5.61529 0.0930516 5.93687C0.00323936 6.21575 0 6.36499 0 10.2065V14.1872H20V10.2065C20 6.34603 19.9971 6.21701 19.9056 5.93272C19.7277 5.38004 19.2508 4.90917 18.662 4.7045C18.4737 4.63907 18.243 4.61934 17.4966 4.60493C16.6531 4.58863 16.5689 4.57999 16.5969 4.5123C16.6139 4.47119 16.6573 4.30587 16.6933 4.14488C17.0093 2.73513 15.9774 1.27553 14.4954 1.03573C13.6569 0.900096 12.5313 1.15813 11.6781 1.68168C11.1007 2.03596 10.4507 2.72878 10.1593 3.30052C10.0868 3.44291 10.0151 3.55946 10 3.55946C9.98493 3.55946 9.91324 3.44291 9.84066 3.30052C9.42221 2.4793 8.51244 1.67466 7.60493 1.32304C6.88 1.04217 5.94235 0.931034 5.36507 1.05753ZM5.49296 2.52726C4.71286 2.86555 4.49385 3.82191 5.05906 4.42201L5.22221 4.59521H7.02423C8.01535 4.59521 8.82629 4.58426 8.82629 4.57089C8.82629 4.51181 8.60751 4.13785 8.37765 3.80407C8.0884 3.38401 7.54484 2.87077 7.20211 2.69406C6.68244 2.42612 5.90178 2.34992 5.49296 2.52726ZM13.288 2.50574C12.6958 2.6584 12.1027 3.10868 11.6547 3.74594C11.4447 4.04455 11.1737 4.5096 11.1737 4.5712C11.1737 4.5844 11.9846 4.59521 12.9758 4.59521H14.7778L14.9409 4.42201C15.1791 4.16915 15.2762 3.89144 15.2473 3.54608C15.2092 3.09049 14.9504 2.72694 14.5308 2.53951C14.2501 2.41414 13.7012 2.39919 13.288 2.50574ZM9.63765 4.9389C9.52826 4.99064 9.04559 5.5748 7.88972 7.05431C7.0115 8.17846 6.27934 9.15013 6.26272 9.21358C6.21606 9.39204 6.26878 9.70592 6.36685 9.83314C6.49338 9.99728 6.76822 10.1342 6.97113 10.1342C7.34681 10.1342 7.40028 10.081 8.72061 8.39268C9.40484 7.51769 9.98056 6.80181 10 6.80181C10.0194 6.80181 10.5991 7.52278 11.2882 8.40394C12.6253 10.1137 12.6461 10.1342 13.0432 10.1342C13.5248 10.1342 13.9196 9.53308 13.7031 9.12928C13.6741 9.07519 12.9441 8.12586 12.0809 7.01972C10.8893 5.49271 10.4736 4.99127 10.3543 4.93696C10.1544 4.84599 9.8323 4.84685 9.63765 4.9389ZM0 16.7493C0 17.2459 0.0192018 17.4346 0.0930516 17.664C0.307887 18.3312 0.890469 18.8247 1.62127 18.9586C1.82606 18.9961 4.1239 19.0063 10.1643 18.9965C18.1008 18.9837 18.4365 18.9798 18.662 18.899C19.2571 18.6856 19.7281 18.2194 19.9056 17.6681C19.981 17.4341 20 17.2489 20 16.7493V16.1236H0V16.7493Z" > < /path>
                </svg>
            );
        case "skins":
            return (
                < svg
                    xmlns = "http://www.w3.org/2000/svg"
                    width = "20px"
                    height = "20px"
                    viewBox = "0 0 24 24"
                    fill = "currentColor" >
                    < path
                        fillRule = "evenodd"
                        clipRule = "evenodd"
                        d = "M12.4264 11.9774C12.2042 12.34 11.8643 13.0037 11.8879 13.0289C12.0118 13.1609 12.9305 13.1192 12.9311 12.9815C12.9313 12.9416 12.5255 11.9974 12.4906 11.9565C12.4742 11.9372 12.4453 11.9466 12.4264 11.9774Z"
                        fill = "currentColor" > < /path>
                    <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M2.53299 2.56264C2.05641 3.42207 1.6665 4.13516 1.6665 4.1473C1.6665 4.15945 2.70823 4.7988 3.9814 5.56808C5.25461 6.3374 6.29718 6.97663 6.29822 6.98861C6.32734 6.98861 9.33485 8.81752 10.5077 9.53075L10.9027 9.77091L11.93 10.3946L11.8557 10.5231C11.5603 11.0343 10.212 13.4884 10.2221 13.4966C10.2996 13.5591 11.0781 14.0159 11.265 14.1086C11.8121 14.3799 12.6475 14.4349 13.1872 14.2351C13.3172 14.1871 13.4324 14.1571 13.4433 14.1685C13.4772 14.2041 13.7699 14.909 13.7699 14.9552C13.7699 14.9792 13.5092 15.4664 13.1907 16.0379C12.8721 16.6094 12.6115 17.0936 12.6115 17.1138C12.6115 17.1516 16.2041 19.3583 16.2245 19.3331C16.275 19.2711 17.3869 17.2414 17.5022 17.001C17.7713 16.4401 17.8884 15.7345 17.8096 15.1487C17.7392 14.6245 17.5877 14.1992 17.2822 13.6674L17.0002 13.1764L17.3069 12.6149C17.4755 12.3061 17.6293 12.0433 17.6486 12.0308C17.6678 12.0183 17.873 12.1241 18.1044 12.266C18.4808 12.4966 18.5335 12.5178 18.6028 12.467C18.8137 12.3124 19.9986 11.318 19.9998 11.2944C20.0014 11.266 18.9757 10.6303 18.9295 10.6311C18.9148 10.6313 18.8129 10.7065 18.7031 10.7982C18.5233 10.9482 18.3393 11.01 18.2746 10.9422C18.2624 10.9294 18.3345 10.7667 18.435 10.5808C18.6021 10.2717 18.6119 10.2384 18.5498 10.1908C18.5124 10.1622 17.3228 9.43484 15.9062 8.57457C14.4895 7.71426 13.3313 7.00058 13.3324 6.98861C13.2454 6.98861 5.66277 2.37915 5.66174 2.32602C5.66134 2.30688 5.72305 2.18026 5.79887 2.04464L5.93672 1.79805L5.54921 1.56122C5.3361 1.43091 5.10454 1.28833 5.03468 1.24433L4.90769 1.16431L4.76161 1.43807C4.68128 1.58864 4.61018 1.71183 4.60363 1.71183C4.59708 1.71183 4.32952 1.55167 4.00905 1.35592C3.68861 1.16016 3.42037 1 3.41294 1C3.40551 1 3.00954 1.70316 2.53299 2.56264ZM11.8879 13.0289C11.8643 13.0037 12.2042 12.34 12.4264 11.9774C12.4453 11.9466 12.4742 11.9372 12.4906 11.9565C12.5255 11.9974 12.9313 12.9416 12.9311 12.9815C12.9305 13.1192 12.0118 13.1609 11.8879 13.0289Z"
                        fill="currentColor"></path>
                    <path
                        d="M10.9027 9.77091L11.93 10.3946L11.8557 10.5231C11.5603 11.0343 10.212 13.4884 10.2221 13.4966C10.2996 13.5591 11.0781 14.0159 11.265 14.1086C11.8121 14.3799 12.6475 14.4349 13.1872 14.2351C13.3172 14.1871 13.4324 14.1571 13.4433 14.1685C13.4772 14.2041 13.7699 14.909 13.7699 14.9552C13.7699 14.9792 13.5092 15.4664 13.1907 16.0379C12.8721 16.6094 12.6115 17.0936 12.6115 17.1138C12.6115 17.1516 16.2041 19.3583 16.2245 19.3331C16.275 19.2711 17.3869 17.2414 17.5022 17.001C17.7713 16.4401 17.8884 15.7345 17.8096 15.1487C17.7392 14.6245 17.5877 14.1992 17.2822 13.6674L17.0002 13.1764L17.3069 12.6149C17.4755 12.3061 17.6293 12.0433 17.6486 12.0308C17.6678 12.0183 17.873 12.1241 18.1044 12.266C18.4808 12.4966 18.5335 12.5178 18.6028 12.467C18.8137 12.3124 19.9986 11.318 19.9998 11.2944C20.0014 11.266 18.9757 10.6303 18.9295 10.6311C18.9148 10.6313 18.8129 10.7065 18.7031 10.7982C18.5233 10.9482 18.3393 11.01 18.2746 10.9422C18.2624 10.9294 18.3345 10.7667 18.435 10.5808C18.6021 10.2717 18.6119 10.2384 18.5498 10.1908C18.5124 10.1622 17.3228 9.43484 15.9062 8.57457C14.4895 7.71426 13.3313 7.00058 13.3324 6.98861C13.2454 6.98861 5.66277 2.37915 5.66173 2.32602C5.66134 2.30688 5.72305 2.18026 5.79887 2.04464L5.93672 1.79805L5.54921 1.56122C5.3361 1.43091 5.10454 1.28833 5.03468 1.24433L4.90769 1.16431L4.76161 1.43807C4.68128 1.58864 4.61018 1.71183 4.60363 1.71183C4.59708 1.71183 4.32953 1.55167 4.00905 1.35592C3.68861 1.16016 3.42037 1 3.41294 1C3.40551 1 3.00954 1.70316 2.53299 2.56264C2.05641 3.42207 1.6665 4.13516 1.6665 4.1473C1.6665 4.15945 2.70823 4.7988 3.9814 5.56808C5.25461 6.3374 6.29718 6.97663 6.29822 6.98861C6.32734 6.98861 9.33485 8.81752 10.5077 9.53075M10.9027 9.77091C10.827 9.7249 10.6893 9.64121 10.5077 9.53075M10.9027 9.77091L10.5077 9.53075M12.4264 11.9774C12.2042 12.34 11.8643 13.0037 11.8879 13.0289C12.0118 13.1609 12.9305 13.1192 12.9311 12.9815C12.9313 12.9416 12.5255 11.9974 12.4906 11.9565C12.4742 11.9372 12.4453 11.9466 12.4264 11.9774Z"
                        stroke="#161B2E"
                        strokeWidth="0.833333"></path>
                    <path
                        d="M1.15094 12.8031C1.2023 12.8408 1.29268 12.8954 1.4202 12.905C1.53341 12.9135 1.62788 12.8823 1.68719 12.8584C1.79592 12.8145 1.93767 12.7288 2.11323 12.6213L1.15098 12.8031C1.15097 12.8031 1.15096 12.8031 1.15094 12.8031ZM1.15094 12.8031C1.03159 12.7157 0.670036 12.4175 0.352194 12.1508C0.19002 12.0146 0.0335579 11.8819 -0.0830574 11.7812C-0.14102 11.7311 -0.191 11.6873 -0.227404 11.6544C-0.245031 11.6385 -0.262878 11.6221 -0.277761 11.6075C-0.28441 11.601 -0.296013 11.5895 -0.308067 11.5759C-0.313372 11.5699 -0.325825 11.5557 -0.339344 11.5365C-0.345907 11.5272 -0.358404 11.5088 -0.371019 11.484L-0.371094 11.4838C-0.379089 11.4681 -0.411055 11.4052 -0.415878 11.3172M1.15094 12.8031L-0.415878 11.3172M-0.415878 11.3172C-0.423302 11.1818 -0.362339 11.0871 -0.351219 11.0698L-0.350424 11.0686C-0.330662 11.0376 -0.310987 11.0158 -0.301964 11.0061C-0.282471 10.9851 -0.264151 10.9696 -0.255802 10.9626C-0.236471 10.9464 -0.215649 10.9311 -0.199245 10.9193C-0.164242 10.8942 -0.118479 10.8633 -0.068716 10.8306C0.0321941 10.7642 0.165136 10.68 0.297949 10.5977C0.430807 10.5153 0.566631 10.433 0.673481 10.3706C0.726355 10.3397 0.775433 10.3118 0.814732 10.2908C0.833687 10.2807 0.8556 10.2693 0.87689 10.2595C0.887005 10.2549 0.903469 10.2476 0.922926 10.2406L0.923008 10.2406C0.924784 10.2399 0.954691 10.2281 0.999207 10.2206M-0.415878 11.3172L0.999207 10.2206M0.999207 10.2206C1.00158 10.1796 1.00937 10.1351 1.02584 10.0888C1.07319 9.95557 1.16684 9.883 1.1966 9.86017L1.19706 9.85981C1.2201 9.84219 1.26244 9.81576 1.28915 9.79909L1.29554 9.79509C1.33553 9.77009 1.39147 9.73544 1.46116 9.69247C1.60074 9.60641 1.79773 9.48562 2.03747 9.33905C2.46652 9.07674 3.03348 8.73127 3.65554 8.35329L3.63794 8.1579H3.97725C4.26548 7.983 4.4172 7.89159 4.47925 7.85483C4.49314 7.8466 4.51117 7.83594 4.52414 7.82895C4.52479 7.8286 4.52563 7.82815 4.52662 7.82762C4.5293 7.8262 4.53313 7.8242 4.53763 7.82197C4.53893 7.82132 4.54066 7.82047 4.54273 7.81948C4.5455 7.81814 4.54889 7.81655 4.55273 7.81483C4.55729 7.81278 4.57249 7.80598 4.59266 7.79931C4.59579 7.79832 4.60037 7.79701 4.60575 7.79555C4.85737 7.64533 5.16573 7.46008 5.51587 7.24891C6.47947 6.66774 7.75657 5.89205 9.03129 5.1145C10.306 4.33696 11.5777 3.55794 12.5306 2.97015C13.0072 2.67616 13.4032 2.43047 13.6798 2.25686C13.723 2.22975 13.7631 2.20449 13.8001 2.18115L13.6996 2.00136L13.504 1.65153L13.846 1.44252L14.2334 1.20574C14.2334 1.20573 14.2335 1.20572 14.2335 1.20572C14.2335 1.20571 14.2335 1.2057 14.2335 1.20569C14.447 1.07512 14.676 0.934109 14.7432 0.89181C14.7432 0.891798 14.7432 0.891786 14.7432 0.891773L14.8702 0.811792L15.2491 0.57303L15.4599 0.968153L15.5498 1.13658L0.999207 10.2206ZM14.0993 7.2273C14.1015 7.22591 14.1038 7.22451 14.106 7.22309C14.2223 7.15042 14.3913 7.04609 14.6009 6.91756C15.0199 6.66069 15.598 6.30904 16.2341 5.9247C16.8712 5.53972 17.4508 5.18704 17.8714 4.92888C18.0816 4.7999 18.2529 4.69401 18.3721 4.61931C18.4314 4.58216 18.4794 4.55168 18.5134 4.52956C18.5299 4.51887 18.5462 4.50806 18.5598 4.49858C18.5657 4.49449 18.5769 4.4866 18.5889 4.4771C18.5937 4.47332 18.6078 4.46208 18.624 4.44632C18.6311 4.43931 18.6491 4.42146 18.6682 4.39567C18.6777 4.38273 18.6941 4.35912 18.7095 4.32687C18.7229 4.29883 18.7502 4.23441 18.7502 4.1473C18.7502 4.09298 18.7391 4.0511 18.7365 4.04145L18.7364 4.04077C18.7322 4.02489 18.7279 4.01249 18.7256 4.00609C18.7209 3.99291 18.7165 3.98256 18.7145 3.9781C18.7102 3.96807 18.7059 3.95931 18.7036 3.95444C18.6983 3.94354 18.6919 3.93112 18.6856 3.91906C18.6727 3.89411 18.6545 3.85987 18.6323 3.81841C18.5875 3.73499 18.5233 3.61674 18.4446 3.47281C18.287 3.18467 18.07 2.7908 17.8314 2.36059L14.0993 7.2273ZM14.0993 7.2273L14.0839 7.40527H13.8102C13.7986 7.41203 13.7859 7.41949 13.772 7.42765C13.6863 7.47815 13.563 7.55165 13.411 7.64282C13.1075 7.82495 12.6927 8.07549 12.2418 8.34861C11.3402 8.89468 10.2956 9.5299 9.70908 9.88658L9.70879 9.88676L9.70874 9.88679L9.31372 10.1269L9.31351 10.1271L8.63316 10.5401C8.82876 10.8878 9.14763 11.4641 9.44087 11.9978C9.62744 12.3374 9.8047 12.6617 9.93484 12.9019C9.99981 13.0219 10.0536 13.1218 10.091 13.1925C10.1095 13.2274 10.1251 13.2572 10.1362 13.2792C10.1414 13.2896 10.1477 13.3021 10.1532 13.314C10.1553 13.3186 10.1611 13.3311 10.1669 13.3464C10.1689 13.3518 10.1768 13.3724 10.1833 13.3995C10.1859 13.4107 10.1938 13.4448 10.1946 13.4892C10.1943 13.5129 10.1879 13.5714 10.1801 13.6057C10.1627 13.6565 10.0948 13.7672 10.0398 13.8207L10.0394 13.821C9.99355 13.858 9.90083 13.9152 9.8259 13.961C9.7363 14.0158 9.6236 14.083 9.5084 14.1506C9.2841 14.2823 9.02932 14.4278 8.92012 14.4819L8.92011 14.4819C8.5866 14.6472 8.18477 14.7388 7.79851 14.7643C7.4576 14.7867 7.1004 14.7595 6.78721 14.6656C6.78 14.6828 6.77275 14.7002 6.76551 14.7176C6.73431 14.7928 6.70429 14.867 6.68008 14.9291C6.70401 14.975 6.73343 15.0307 6.76767 15.0947C6.87123 15.2883 7.01482 15.5508 7.17327 15.835C7.17327 15.835 7.17327 15.8351 7.17328 15.8351L6.80933 16.0379C7.12789 16.6094 7.38853 17.0936 7.38853 17.1138L14.0993 7.2273ZM2.19036 15.1487L1.7774 15.0932C1.85612 14.5076 2.02829 14.0311 2.35646 13.4599L2.52226 13.1712L2.32748 12.8147L2.68359 12.6201L2.32748 12.8147C2.28045 12.7286 2.2348 12.6465 2.19327 12.5731L2.19036 15.1487ZM15.7738 1.00034C15.9348 0.901955 16.0832 0.812221 16.1928 0.746802C16.2473 0.71422 16.2934 0.68699 16.327 0.667548C16.3434 0.658047 16.359 0.64913 16.3719 0.642041C16.3778 0.638811 16.3869 0.633843 16.3969 0.628888C16.4012 0.62673 16.4114 0.621728 16.4242 0.61635C16.4302 0.613814 16.4429 0.608594 16.4594 0.603313C16.4596 0.603262 16.4598 0.603182 16.4601 0.603076C16.4677 0.600561 16.5198 0.583333 16.5871 0.583333C16.683 0.583333 16.7526 0.616325 16.7807 0.63102C16.8138 0.648393 16.8376 0.666601 16.85 0.676742C16.8751 0.697116 16.8922 0.715939 16.8985 0.723036C16.9131 0.739351 16.9233 0.753435 16.9264 0.757789C16.9346 0.769109 16.9412 0.779261 16.944 0.783766C16.951 0.794675 16.9585 0.807086 16.9653 0.818454C16.9795 0.842315 16.9988 0.875478 17.0219 0.915648C17.0684 0.996597 17.1342 1.11261 17.2142 1.25457C17.3742 1.53881 17.5928 1.9303 17.8314 2.36057L15.7738 1.00034ZM3.4519 19.5956C3.42705 19.565 3.40371 19.5255 3.39743 19.5148L3.39629 19.5129C3.38346 19.4914 3.36744 19.4638 3.34925 19.4321C3.31265 19.3681 3.26374 19.2812 3.20691 19.1794C3.09307 18.9754 2.94518 18.7073 2.79554 18.4342C2.50052 17.8957 2.18601 17.3145 2.12208 17.1812L3.4519 19.5956Z"
                        fill="currentColor"
                        stroke="#161B2E"
                        strokeWidth="0.833333"></path>
                    <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M7.5736 11.9774C7.79581 12.34 8.13571 13.0037 8.11206 13.0289C7.98819 13.1609 7.06953 13.1192 7.06885 12.9815C7.06865 12.9416 7.47445 11.9974 7.50937 11.9565C7.52582 11.9372 7.55474 11.9466 7.5736 11.9774Z"
                        fill="currentColor"></path>
                    <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M17.467 2.56264C17.9436 3.42207 18.3335 4.13516 18.3335 4.1473C18.3335 4.15945 17.2918 4.7988 16.0186 5.56808C14.7454 6.3374 13.7028 6.97663 13.7018 6.98861C13.6727 6.98861 10.6651 8.81752 9.49229 9.53075L9.09727 9.77091L8.07 10.3946L8.1443 10.5231C8.43973 11.0343 9.78804 13.4884 9.77793 13.4966C9.70044 13.5591 8.92195 14.0159 8.73504 14.1086C8.18788 14.3799 7.35254 14.4349 6.81276 14.2351C6.68282 14.1871 6.56758 14.1571 6.55668 14.1685C6.52276 14.2041 6.23013 14.909 6.23013 14.9552C6.23013 14.9792 6.49077 15.4664 6.80933 16.0379C7.12789 16.6094 7.38853 17.0936 7.38853 17.1138C7.38853 17.1516 3.79591 19.3583 3.77546 19.3331C3.72505 19.2711 2.61309 17.2414 2.49777 17.001C2.2287 16.4401 2.11162 15.7345 2.19036 15.1487C2.26082 14.6245 2.41225 14.1992 2.71775 13.6674L2.99984 13.1764L2.69314 12.6149C2.52446 12.3061 2.37067 12.0433 2.35141 12.0308C2.33216 12.0183 2.12704 12.1241 1.89556 12.266C1.51924 12.4966 1.46655 12.5178 1.39724 12.467C1.18625 12.3124 0.00144196 11.318 0.000164032 11.2944C-0.00139427 11.266 1.02432 10.6303 1.07054 10.6311C1.0852 10.6313 1.18705 10.7065 1.2969 10.7982C1.47674 10.9482 1.66068 11.01 1.72536 10.9422C1.73762 10.9294 1.66548 10.7667 1.56497 10.5808C1.39788 10.2717 1.3881 10.2384 1.45021 10.1908C1.4876 10.1622 2.67721 9.43484 4.09382 8.57457C5.51048 7.71426 6.66868 7.00058 6.6676 6.98861C6.75456 6.98861 14.3372 2.37915 14.3383 2.32602C14.3387 2.30688 14.2769 2.18026 14.2011 2.04464L14.0633 1.79805L14.4508 1.56122C14.6639 1.43091 14.8955 1.28833 14.9653 1.24433L15.0923 1.16431L15.2384 1.43807C15.3187 1.58864 15.3898 1.71183 15.3964 1.71183C15.4029 1.71183 15.6705 1.55167 15.991 1.35592C16.3114 1.16016 16.5796 1 16.5871 1C16.5945 1 16.9905 1.70316 17.467 2.56264ZM8.11206 13.0289C8.13571 13.0037 7.79581 12.34 7.5736 11.9774C7.55474 11.9466 7.52582 11.9372 7.50937 11.9565C7.47445 11.9974 7.06865 12.9416 7.06885 12.9815C7.06953 13.1192 7.98819 13.1609 8.11206 13.0289Z"
                        fill="currentColor"></path>
                    <path
                        d="M9.09727 9.77091L8.07 10.3946L8.1443 10.5231C8.43973 11.0343 9.78804 13.4884 9.77793 13.4966C9.70044 13.5591 8.92195 14.0159 8.73504 14.1086C8.18788 14.3799 7.35254 14.4349 6.81276 14.2351C6.68282 14.1871 6.56758 14.1571 6.55668 14.1685C6.52276 14.2041 6.23013 14.909 6.23013 14.9552C6.23013 14.9792 6.49077 15.4664 6.80933 16.0379C7.12789 16.6094 7.38853 17.0936 7.38853 17.1138C7.38853 17.1516 3.79591 19.3583 3.77546 19.3331C3.72505 19.2711 2.61309 17.2414 2.49777 17.001C2.2287 16.4401 2.11162 15.7345 2.19036 15.1487C2.26082 14.6245 2.41225 14.1992 2.71775 13.6674L2.99984 13.1764L2.69314 12.6149C2.52446 12.3061 2.37067 12.0433 2.35141 12.0308C2.33216 12.0183 2.12704 12.1241 1.89556 12.266C1.51924 12.4966 1.46655 12.5178 1.39724 12.467C1.18625 12.3124 0.00144253 11.318 0.00016429 11.2944C-0.00139357 11.266 1.02432 10.6303 1.07053 10.6311C1.08519 10.6313 1.18705 10.7065 1.2969 10.7982C1.47674 10.9482 1.66068 11.01 1.72535 10.9422C1.73762 10.9294 1.66548 10.7667 1.56497 10.5808C1.39788 10.2717 1.3881 10.2384 1.45021 10.1908C1.4876 10.1622 2.67721 9.43484 4.09382 8.57457C5.51048 7.71426 6.66868 7.00058 6.6676 6.98861C6.75456 6.98861 14.3372 2.37915 14.3383 2.32602C14.3387 2.30688 14.2769 2.18026 14.2011 2.04464L14.0633 1.79805L14.4508 1.56122C14.6639 1.43091 14.8955 1.28833 14.9653 1.24433L15.0923 1.16431L15.2384 1.43807C15.3187 1.58864 15.3898 1.71183 15.3964 1.71183C15.4029 1.71183 15.6705 1.55167 15.991 1.35592C16.3114 1.16016 16.5796 1 16.5871 1C16.5945 1 16.9905 1.70316 17.467 2.56264C17.9436 3.42207 18.3335 4.13516 18.3335 4.1473C18.3335 4.15945 17.2918 4.7988 16.0186 5.56808C14.7454 6.3374 13.7028 6.97663 13.7018 6.98861C13.6727 6.98861 10.6651 8.81752 9.49229 9.53075M9.09727 9.77091C9.17302 9.7249 9.31066 9.64121 9.49229 9.53075M9.09727 9.77091L9.49229 9.53075M7.5736 11.9774C7.79581 12.34 8.13571 13.0037 8.11206 13.0289C7.98819 13.1609 7.06953 13.1192 7.06885 12.9815C7.06865 12.9416 7.47446 11.9974 7.50937 11.9565C7.52582 11.9372 7.55474 11.9466 7.5736 11.9774Z"
                        stroke="#161B2E"
                        strokeWidth="0.833333"></path>
                </svg>
            );
        case "software":
                return (
                    < svg
                        xmlns = "http://www.w3.org/2000/svg"
                        width = "20px"
                        height = "20px"
                        viewBox = "0 0 24 24"
                        fill = "currentColor" >
                        < path
                            fillRule = "evenodd"
                            clipRule = "evenodd"
                            d = "M1.97439 3.04096C1.60183 3.12064 1.27808 3.36415 1.10506 3.69479L1 3.89556V16.1239L1.10506 16.3247C1.23867 16.5801 1.46255 16.7833 1.74384 16.9046L1.96498 17H18.0078L18.229 16.9046C18.3506 16.8522 18.5204 16.7454 18.6064 16.6674C18.6924 16.5893 18.81 16.4351 18.8678 16.3247L18.9728 16.1239L18.9942 5.33741H14.0862C9.90097 5.33741 9.16227 5.32971 9.06968 5.28506C9.00997 5.25626 8.40939 4.73063 7.73502 4.11698L6.50893 3.00124L4.31737 3.00489C3.11203 3.00694 2.0577 3.02314 1.97439 3.04096ZM9.60351 4.16933H19L18.9728 4.01419C18.9043 3.62401 18.6241 3.2853 18.229 3.11487L18.0078 3.01949L8.31779 3L9.60351 4.16933ZM2.58507 4.25233C2.15726 4.51406 2.15726 4.99268 2.58507 5.2544C2.97958 5.49579 3.55319 5.19896 3.55319 4.75337C3.55319 4.30778 2.97958 4.01094 2.58507 4.25233ZM5.15836 4.25233C4.74084 4.50778 4.72958 4.97056 5.13427 5.24279C5.29277 5.34938 5.67301 5.35 5.82954 5.24389C6.04051 5.10087 6.10637 4.98421 6.10637 4.75337C6.10637 4.51391 6.03352 4.39254 5.80522 4.25146C5.6242 4.13961 5.34194 4.13998 5.15836 4.25233ZM10.3246 8.91069C10.2563 8.94851 10.1625 9.02319 10.1162 9.07663C9.99646 9.21483 8.69977 12.7626 8.69977 12.9519C8.69977 13.1289 8.81995 13.3097 9.01829 13.431C9.25612 13.5765 9.65164 13.5259 9.85686 13.3236C9.95742 13.2245 11.3133 9.593 11.3133 9.42271C11.3133 9.37044 11.2716 9.25615 11.2207 9.16873C11.0535 8.88167 10.6085 8.75354 10.3246 8.91069ZM7.10674 9.48356C7.03774 9.51444 6.65517 9.83859 6.25655 10.2039C5.17902 11.1912 5.17902 11.1645 6.25727 12.1525C6.65629 12.5181 7.0416 12.8426 7.11353 12.8736C7.60471 13.0852 8.13927 12.7034 8.03799 12.2133C8.01555 12.1047 7.91419 11.9893 7.52389 11.6279L7.03778 11.1778L7.52389 10.7277C7.91419 10.3663 8.01555 10.2509 8.03799 10.1424C8.07614 9.95777 8.03549 9.8023 7.91009 9.65301C7.72879 9.43724 7.37621 9.36285 7.10674 9.48356ZM12.244 9.48761C11.9471 9.62045 11.806 10.0427 11.975 10.2926C12.0124 10.3478 12.2451 10.5689 12.4923 10.7838L12.9417 11.1746L12.4524 11.6051C12.0477 11.9612 11.9553 12.0626 11.9179 12.1914C11.84 12.4597 11.9861 12.7555 12.2534 12.8707C12.4228 12.9437 12.6883 12.9472 12.8331 12.8783C12.8928 12.8499 13.2793 12.517 13.692 12.1385C14.3206 11.5619 14.4469 11.4281 14.4705 11.314C14.534 11.0067 14.4992 10.9577 13.692 10.2159C13.2793 9.83676 12.8835 9.50382 12.8123 9.47611C12.64 9.40899 12.4094 9.4137 12.244 9.48761Z"
                            fill = "currentColor" > < /path>
                    </svg>
            );
        case "vr-games":
                return (
                    < svg
                        width = "20px"
                        height = "20px"
                        viewBox = "0 0 24 24"
                        fill = "currentColor"
                        xmlns = "http://www.w3.org/2000/svg" >
                        < path
                            d = "M10.0001 3.46822C12.3214 3.46822 14.4076 4.22233 15.8126 5.57879C16.9682 5.72164 18.0584 6.16582 18.96 6.86114C17.3838 3.74351 14.1188 2 10.0001 2C5.93771 2 2.65025 3.74116 1.05528 6.84937C1.88181 6.21586 2.86675 5.79184 3.91649 5.61762C5.29522 4.20233 7.46768 3.46822 10.0001 3.46822Z"
                            fill = "currentColor" > < /path>
                        <path
                            d="M15.0001 16.1175C13.6997 16.1175 12.4506 15.6406 11.5176 14.7881L11.5151 14.7858C11.4075 14.7095 11.2919 14.6437 11.1701 14.5893C10.8033 14.4306 10.4039 14.3499 10.0001 14.3528C9.48265 14.3528 9.08891 14.474 8.83141 14.5893C8.70924 14.6436 8.59324 14.7094 8.48517 14.7858L8.48267 14.7881C7.89495 15.3251 7.17605 15.718 6.38918 15.9322C5.60231 16.1464 4.77157 16.1754 3.97002 16.0166C3.16846 15.8578 2.42063 15.5161 1.79229 15.0215C1.16395 14.5269 0.674334 13.8946 0.366503 13.1802C0.0586717 12.4657 -0.0579491 11.6911 0.0268944 10.9243C0.111738 10.1575 0.395447 9.42203 0.853076 8.78262C1.3107 8.14321 1.92824 7.61942 2.65137 7.25729C3.37451 6.89516 4.1811 6.7058 5.00022 6.70584H15.0001C16.3261 6.70584 17.5979 7.20164 18.5356 8.08415C19.4732 8.96667 20 10.1636 20 11.4117C20 12.6598 19.4732 13.8567 18.5356 14.7392C17.5979 15.6217 16.3261 16.1175 15.0001 16.1175Z"
                            fill="currentColor"></path>
                        <path
                            d="M14.9998 17.0001C13.6995 17 12.4504 16.5232 11.5174 15.6707L11.5149 15.6684C11.4072 15.5921 11.2916 15.5263 11.1699 15.4719C10.8031 15.3132 10.4036 15.2325 9.9999 15.2354C9.4824 15.2354 9.08866 15.3566 8.83116 15.4719C8.70899 15.5262 8.59299 15.592 8.48492 15.6684L8.48242 15.6707C7.8947 16.2077 7.1758 16.6006 6.38893 16.8148C5.93674 16.9379 5.47006 16.9999 5.00275 17.0001H14.9998Z"
                            fill="currentColor"></path>
                        <path
                            d="M5.00018 8.17661C4.58233 8.17661 4.16858 8.25649 3.78255 8.41168C3.39651 8.56688 3.04575 8.79436 2.75029 9.08112C2.45483 9.36789 2.22046 9.70833 2.06056 10.083C1.90066 10.4577 1.81836 10.8593 1.81836 11.2648C1.81836 11.6704 1.90066 12.0719 2.06056 12.4466C2.22046 12.8213 2.45483 13.1617 2.75029 13.4485C3.04575 13.7353 3.39651 13.9628 3.78255 14.118C4.16858 14.2731 4.58234 14.353 5.00018 14.353L5.00018 13.4266C4.70769 13.4266 4.41806 13.3706 4.14784 13.262C3.87761 13.1534 3.63208 12.9941 3.42526 12.7934C3.21844 12.5927 3.05438 12.3544 2.94245 12.0921C2.83051 11.8298 2.7729 11.5487 2.7729 11.2648C2.7729 10.9809 2.83051 10.6998 2.94245 10.4376C3.05438 10.1753 3.21844 9.93697 3.42526 9.73623C3.63208 9.5355 3.87761 9.37626 4.14784 9.26762C4.41806 9.15899 4.70769 9.10307 5.00018 9.10307L5.00018 8.17661Z"
                            fill="#161B2E"></path>
                        <path
                            d="M14.9993 8.17661C15.4172 8.17661 15.8309 8.25649 16.217 8.41168C16.603 8.56688 16.9538 8.79436 17.2492 9.08112C17.5447 9.36789 17.779 9.70833 17.939 10.083C18.0989 10.4577 18.1812 10.8593 18.1812 11.2648C18.1812 11.6704 18.0989 12.0719 17.939 12.4466C17.779 12.8213 17.5447 13.1617 17.2492 13.4485C16.9538 13.7353 16.603 13.9628 16.217 14.118C15.8309 14.2731 15.4172 14.353 14.9993 14.353L14.9993 13.4266C15.2918 13.4266 15.5814 13.3706 15.8517 13.262C16.1219 13.1534 16.3674 12.9941 16.5743 12.7934C16.7811 12.5927 16.9451 12.3544 17.0571 12.0921C17.169 11.8298 17.2266 11.5487 17.2266 11.2648C17.2266 10.9809 17.169 10.6998 17.0571 10.4376C16.9451 10.1753 16.7811 9.93697 16.5743 9.73623C16.3674 9.5355 16.1219 9.37626 15.8517 9.26762C15.5814 9.15899 15.2918 9.10307 14.9993 9.10307L14.9993 8.17661Z"
                            fill="#161B2E"></path>
                    </svg>
            );
        case "weekly-deals":
            return (
                    < svg
                        width = "20px"
                        height = "20px"
                        viewBox = "0 0 24 24" >
                        < path
                            fill = "currentColor"
                            d = "M12 7a8 8 0 1 1 0 16a8 8 0 0 1 0-16m0 3.5l-1.322 2.68l-2.958.43l2.14 2.085l-.505 2.946L12 17.25l2.645 1.39l-.505-2.945l2.14-2.086l-2.958-.43zm1-8.501L18 2v3l-1.363 1.138A9.9 9.9 0 0 0 13 5.05zm-2 0v3.05a9.9 9.9 0 0 0-3.636 1.088L6 5V2z" > < /path>
                    </svg>
            );
        default:
            return (
                < svg
                    width = "20px"
                    height = "20px"
                    viewBox = "0 0 24 24" >
                    < path
                        fill = "currentColor"
                        d = "M12 7a8 8 0 1 1 0 16a8 8 0 0 1 0-16m0 3.5l-1.322 2.68l-2.958.43l2.14 2.085l-.505 2.946L12 17.25l2.645 1.39l-.505-2.945l2.14-2.086l-2.958-.43zm1-8.501L18 2v3l-1.363 1.138A9.9 9.9 0 0 0 13 5.05zm-2 0v3.05a9.9 9.9 0 0 0-3.636 1.088L6 5V2z" > < /path>
                </svg>
            );
    }
};

export const backgroundImageFun = (value) => {
    switch (value) {
        case "psn":
            return `url('${window.origin}/assets/images/psn_bg.png')`;
        case "xbox":
            return `url('${window.origin}/assets/images/xbox_bg.png')`;
        case "nintendo":
            return `url('${window.origin}/assets/images/nintendo_bg.png')`;
        case "gift-cards":
            return `url('${window.origin}/assets/images/gift_card_bg.png')`;
        case "pc-gaming":
            return `url('${window.origin}/assets/images/pc_gaming_bg.png')`;
        case "skins":
            return `url('${window.origin}/assets/images/skins_bg.png')`;
        case "software":
            return `url('${window.origin}/assets/images/software_bg.png')`;
        case "vr-games":
            return `url('${window.origin}/assets/images/vr_game_bg.png')`;
        case "weekly-deals":
            return `url('${window.origin}/assets/images/weekly_deal_bg.png')`;
        default:
            return `url('${window.origin}/assets/images/psn_bg.png')`;
    }
};

export const getCheckIcon = () => {
    return (
        <span className="icon">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                          <path
                              fill="currentColor"
                              d="m9.55 18l-5.7-5.7l1.425-1.425L9.55 15.15l9.175-9.175L20.15 7.4z"></path>
                        </svg>
                      </span>
    )
}