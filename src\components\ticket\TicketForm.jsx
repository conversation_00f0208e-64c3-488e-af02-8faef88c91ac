import useTicketForm from "../../hooks/ticket/useTicketForm.js";
import {useProfile} from "../../hooks/auth/useProfile.js";
import FileUpload from "./FileUpload.jsx";
import {inquiryOptions, supportSubjects} from "../../constant/ticketOptions.js";
import {useContext} from "react";
import {ModalContext} from "../../store/ModalContext.jsx";
import {getAccessToken} from "../../vbrae-utils/index.js";

export default function TicketForm({setCompleted}){

    const { OpenModal } = useContext(ModalContext);
    const hasUser = !!getAccessToken();

    const handleSuccess = () => {
        setCompleted(true)
    }

    const handleSubmit = (e) => {
        if(!hasUser) OpenModal("login")
        formik.handleSubmit(e)
    }

    const {user} = useProfile();

    const {formik} = useTicketForm({userName:user?.name, handleSuccess});

    return (
        <form className="col col-md-10 col-lg-8 col-xl-6 vb_req" onSubmit={handleSubmit}>
            <p className="vb_title mb-4">Submit a Request</p>

            <div className="col mb-4">
                <p className="vb_head mb-2">Your email address</p>
                <input
                    type="email"
                    className="vb_contact_input"
                    {...formik.getFieldProps("email")}
                />
                {formik.touched.email &&
                    <small className="main_footer_copy text-danger">{formik.errors.email}</small>}
            </div>
            <div className="col mb-4">
                <p className="vb_head mb-2">Subject</p>
                <select className="vb_contact_sel" {...formik.getFieldProps("subject")}>
                    {supportSubjects.map((item, index) => (
                        <option key={index} value={item.title}>{item.title}</option>
                    ))}
                </select>
            </div>
            <div className="col mb-4">
                <p className="vb_head mb-2">
                    What is you inquiry about?
                </p>
                <select className="vb_contact_sel" {...formik.getFieldProps("inquiryType")}>
                    {inquiryOptions.map((item, index) => (
                        <option key={index} value={item.title}>{item.title}</option>
                    ))}
                </select>
            </div>
            <div className="col mb-4">
                <p className="vb_head mb-2">Description</p>
                <textarea
                    className="vb_contact_input h-auto"
                    id="description"
                    cols="30"
                    rows="5"
                    {...formik.getFieldProps("description")}
                />
                {formik.touched.description &&
                    <small className="main_footer_copy text-danger">{formik.errors.description}</small>}
            </div>
            <FileUpload onFilesSelected={files=> formik.setFieldValue("attachments", files)}/>

            <button
                disabled={formik.isSubmitting}
                type="submit"
                className="col-12 col-md-auto vb_contact_btn2">
                {formik.isSubmitting ? "Processing..." : "Send Message"}
            </button>
        </form>
    )
}