import {useTimer} from "react-timer-hook";
import {Link} from "react-router-dom";
import {getIcon} from "../../vbrae-utils/lib/getIcons.jsx";

export default function BannerThree({endTime, images, link, discountPercent, category, existingPrice, title}){

    const { seconds, minutes, hours, days } = useTimer({
        expiryTimestamp: endTime,
        onExpire: () => console.warn("onExpire called"),
    });

    const [p1, p2] = existingPrice.split(".");

    return (
        <Link to={link} className="col banner_sm1_con" target="_blank" style={{backgroundImage: `url(${images.desktop})`}}>
            <div className="banner_sm2_col">
                <div className="banner_sm2_cont d-flex flex-column">
                    <div className="d-flex justify-content-between align-items-start mb-3">
                        <div className="d-flex align-items-end">
                            <p className="banner_sm_timing text-center me-2 mb-0">
                                <span>OFFER</span> <br/>
                                {discountPercent}% OFF
                            </p>
                            <p className="banner_sm_time">{days}d | {hours}h | {minutes}m | {seconds}s</p>
                        </div>
                        <span className="banner_sm_icon">
                                {getIcon(category)}
                              </span>
                    </div>

                    <p className="banner_sm_text mb-0">
                        {title}
                    </p>

                    <div className="d-flex align-items-end mt-auto">
                        <p className="banner_sm_price me-2 mb-0">
                            ${p1}{p2 && <span>.{p2}</span>}
                        </p>
                        <p className="banner_sm_price_sm mb-0">${p1}{p2 && <>.{p2}</>}</p>
                    </div>
                </div>
            </div>
        </Link>
    )
}