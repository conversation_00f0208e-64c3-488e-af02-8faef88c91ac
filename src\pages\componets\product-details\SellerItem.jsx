import {Link} from "react-router-dom";
import {useAddCart} from "../../../hooks/cart/useAddCart.js";
import {getAccessToken} from "../../../vbrae-utils/index.js";
import {useContext} from "react";
import {ModalContext} from "../../../store/ModalContext.jsx";
import Spinner from "../../../components/common/Spinner.jsx";

export default function SellerItem({seller, deliveryTime, customerPays, inCart, _id}){

    const { OpenModal } = useContext(ModalContext);
    const hasUser = !!getAccessToken();

    const [p1, p2] = customerPays.toString().split(".");

    const {cartLoading, cartRefetch} = useAddCart({quantity: 1, offerId: _id});

    const handleCartClick = () => {
        if(hasUser) cartRefetch().finally();
        else OpenModal("login")
    }

    const {sellerStats} = seller;

    return (
        <div
            className="col details_offer active d-flex flex-wrap gap-2 gap-md-4 justify-content-between align-items-center mb-3">
            <div className="col d-flex flex-wrap align-items-center gap-3">
                <div className="col d-flex align-items-center gap-2">
                    <Link to={"/shop-details"}>
                        <div className="col-auto position-relative">
                            <img
                                src={window.origin + "/assets/images/icons/verify.svg"}
                                alt=""
                                className="details_profile_verify position-absolute"
                            />
                            <img
                                src={seller.avatar || window.origin + "/assets/images/user.png"}
                                alt=""
                                className="details_profile_img"
                            />
                        </div>
                    </Link>
                    <div className="col">
                        <div className="d-flex gap-3 align-items-center">
                            <p className="details_profile_name">
                                Seller
                                <Link to={"/shop-details"}>
                                    <span className="text-white"> {seller.name}</span>
                                </Link>
                            </p>
                            <span className="details_profile_tag position-relative d-flex align-items-center">
                                <svg
                                    className="position-absolute"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 24 24">
                                  <path
                                      fill="currentColor"
                                      d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                                  />
                                </svg>
                                {sellerStats.averageRating.toFixed(1)}
                              </span>
                            <span className="details_profile_tag seller position-relative d-flex align-items-center">
                                <svg
                                    className="position-absolute"
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="1em"
                                    height="1em"
                                    viewBox="0 0 256 256">
                                  <path
                                      fill="currentColor"
                                      d="M239.75 90.81c0 .11 0 .21-.07.32L217 195a16 16 0 0 1-15.72 13H54.71A16 16 0 0 1 39 195L16.32 91.13c0-.11-.05-.21-.07-.32A16 16 0 0 1 44 77.39l33.67 36.29l35.8-80.29a1 1 0 0 0 0-.1a16 16 0 0 1 29.06 0a1 1 0 0 0 0 .1l35.8 80.29L212 77.39a16 16 0 0 1 27.71 13.42Z"
                                  />
                                </svg>
                                {sellerStats.tier}
                              </span>
                        </div>
                        <p className="details_profile_name">{sellerStats.totalOrders} Orders</p>
                    </div>
                </div>

                <div className="col-12 col-md-auto d-flex align-items-center gap-2">
                    <span className="game_badge d-flex align-items-center type5">
                            <svg
                                className="me-1"
                                xmlns="http://www.w3.org/2000/svg"
                                width="1em"
                                height="1em"
                                viewBox="0 0 24 24">
                              <path
                                  fill="currentColor"
                                  d="M12 8H4a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h1v4a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-4h3l5 4V4zm9.5 4c0 1.71-.96 3.26-2.5 4V8c1.53.75 2.5 2.3 2.5 4"
                              />
                            </svg>
                            Promoted
                          </span>
                    <span className="game_badge d-flex align-items-center type6">
                            {deliveryTime}{deliveryTime === 'instant' ? '' : 'H'}
                          </span>
                    <hr className="details_offer_line d-none d-md-block ms-2"></hr>
                </div>
            </div>

            <div
                className="col-12 col-md-auto d-flex justify-content-between gap-4 align-items-end align-items-md-center">
                <div className="col-auto text-end">
                    <p className="details_offer_price mb-0">
                        ${p1}{p2 && <span>.{p2}</span>}
                    </p>
                </div>
                {cartLoading ? <Spinner /> :
                    <div className="game_item_cart d-flex">
                        {inCart ? <div className="game_item_cart active d-flex position-relative">
                  <span className="icon d-flex justify-content-center align-items-center">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          fillRule="evenodd"
                          d="M14.665 2.33a.75.75 0 0 1 1.006.335l2.201 4.402c1.353.104 2.202.37 2.75 1.047c.9 1.114.541 2.79-.177 6.143l-.429 2c-.487 2.273-.73 3.409-1.555 4.076S16.474 21 14.15 21h-4.3c-2.324 0-3.486 0-4.31-.667c-.826-.667-1.07-1.803-1.556-4.076l-.429-2c-.718-3.353-1.078-5.029-.177-6.143c.548-.678 1.397-.943 2.75-1.047l2.201-4.402a.75.75 0 0 1 1.342.67l-1.835 3.67Q8.559 7 9.422 7h5.156q.863-.001 1.586.005l-1.835-3.67a.75.75 0 0 1 .336-1.006M7.25 12a.75.75 0 0 1 .75-.75h8a.75.75 0 0 1 0 1.5H8a.75.75 0 0 1-.75-.75M10 14.25a.75.75 0 0 0 0 1.5h4a.75.75 0 0 0 0-1.5z"
                          clipRule="evenodd"
                      />
                    </svg>
                  </span>
                            <span className="check position-absolute">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          fillRule="evenodd"
                          d="M12 21a9 9 0 1 0 0-18a9 9 0 0 0 0 18m-.232-5.36l5-6l-1.536-1.28l-4.3 5.159l-2.225-2.226l-1.414 1.414l3 3l.774.774z"
                          clipRule="evenodd"
                      />
                    </svg>
                  </span>
                        </div> : <span className="icon d-flex justify-content-center align-items-center"
                                       onClick={handleCartClick}>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="1em"
                        height="1em"
                        viewBox="0 0 24 24">
                      <path
                          fill="currentColor"
                          fillRule="evenodd"
                          d="M14.665 2.33a.75.75 0 0 1 1.006.335l2.201 4.402c1.353.104 2.202.37 2.75 1.047c.9 1.114.541 2.79-.177 6.143l-.429 2c-.487 2.273-.73 3.409-1.555 4.076S16.474 21 14.15 21h-4.3c-2.324 0-3.486 0-4.31-.667c-.826-.667-1.07-1.803-1.556-4.076l-.429-2c-.718-3.353-1.078-5.029-.177-6.143c.548-.678 1.397-.943 2.75-1.047l2.201-4.402a.75.75 0 0 1 1.342.67l-1.835 3.67Q8.559 7 9.422 7h5.156q.863-.001 1.586.005l-1.835-3.67a.75.75 0 0 1 .336-1.006M7.25 12a.75.75 0 0 1 .75-.75h8a.75.75 0 0 1 0 1.5H8a.75.75 0 0 1-.75-.75M10 14.25a.75.75 0 0 0 0 1.5h4a.75.75 0 0 0 0-1.5z"
                          clipRule="evenodd"
                      />
                    </svg>
                  </span>}
                    </div>}
            </div>
        </div>
    )
}