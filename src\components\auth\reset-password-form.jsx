import useResetPassword from "../../hooks/auth/useResetPassword.js";

export default function ResetPasswordForm(){

    const {formik} = useResetPassword()

    return (
        <form onSubmit={formik.handleSubmit}>
            <div className="col">
                <div className="col mb-3">
                    <label
                        htmlFor="Password"
                        className="auth_label text-uppercase mb-2">
                        Password
                    </label>
                    <input
                        type="password"
                        name="password"
                        className="auth_input"
                        {...formik.getFieldProps('password')}
                    />
                    {formik.touched.password && <small className="main_footer_copy text-danger">{formik.errors.password}</small>}
                </div>

                <div className="col mb-3">
                    <label
                        htmlFor="Confirm Password"
                        className="auth_label text-uppercase mb-2">
                        Confirm Password
                    </label>
                    <input
                        type="password"
                        name="confirmPassword"
                        className="auth_input"
                        {...formik.getFieldProps('confirmPassword')}
                    />
                    {formik.touched.confirmPassword && <small className="main_footer_copy text-danger">{formik.errors.confirmPassword}</small>}
                </div>
            </div>

            <div className="col d-md-flex align-items-center gap-4">
                <button disabled={formik.isSubmitting || !formik.isValid} className="col-12 col-md-auto auth_btn">
                    {formik.isSubmitting ? "Processing..." : "Reset Password"}
                </button>
            </div>
        </form>
    )
}