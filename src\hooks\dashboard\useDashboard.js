import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getDashboard} from "../../api/dashboard/getDashboard.js";
import {useProfile} from "../auth/useProfile.js";

export const useDashboard = ({year}) => {

    const {user} = useProfile();

    const { data: dashboard, loading: dashboardLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['dashboard', year],
            queryFn: () => getDashboard({sellerId: user._id, year}),
            onError: showError,
            enabled: !!user,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { dashboard, dashboardLoading};
};