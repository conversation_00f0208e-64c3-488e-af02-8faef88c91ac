import {convertToDays} from "../../vbrae-utils/lib/time.js";

export default function ProductReviews({ratings, averageRating}){

    return (
        <div className="col details_tab_cont">
            <div className="col d-flex flex-wrap justify-content-between gap-3 align-items-center mb-4">
                <div className="col d-flex justify-content-between align-items-center">
                    <div className="col-auto">
                        <p className="details_title">{averageRating.toFixed(1)} out of 5</p>
                        <p className="details_tit">{ratings.length} Reviews</p>
                    </div>
                    <button
                        type="button"
                        className="d-md-none details_rev_button">
                        Add Review
                    </button>
                </div>
                <select
                    name=""
                    className="col-12 col-md-auto details_fil_sel">
                    <option value="">Sort by: Best price</option>
                </select>
            </div>

            <div className="col d-flex flex-column gap-3 mb-4">
                {ratings.map((rating, index) => (
                    <RatingRow {...rating} key={index} />
                ))}
            </div>

            {/*<div className="d-flex justify-content-center">*/}
            {/*    <button type="button" className="details_rev_button">*/}
            {/*        Load more*/}
            {/*    </button>*/}
            {/*</div>*/}
        </div>
    )
}

const RatingRow = ({content, stars, userName, userAvatar, createdAt, sellerResponse, sellerAvatar, sellerName}) =>  {
    const totalStars = 5;
    return (
        <div className="col details_rev">
            <div className="d-flex gap-1 mb-2">
                {[...Array(totalStars)].map((_, index) => (
                    <span className="details_rev_star" key={index}>
                      <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="1em"
                          height="1em"
                          viewBox="0 0 24 24"
                      >
                        <path
                            fill={index < stars ? "currentColor" : "lightgray"} // Filled or unfilled color
                            d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                        />
                      </svg>
                    </span>
                ))}
            </div>
            <p className="details_rev_text mb-3">
                {content}
            </p>

            <div className="d-flex gap-2 align-items-center mb-3">
                <p className="details_rev_hint">Helpful?</p>
                <span className="details_rev_like d-flex align-items-center">
                              <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="m20.27 16.265l.705-4.08a1.666 1.666 0 0 0-1.64-1.95h-5.181a.833.833 0 0 1-.822-.969l.663-4.045a4.8 4.8 0 0 0-.09-1.973a1.64 1.64 0 0 0-1.092-1.137l-.145-.047a1.35 1.35 0 0 0-.994.068c-.34.164-.588.463-.68.818l-.476 1.834a7.6 7.6 0 0 1-.656 1.679c-.415.777-1.057 1.4-1.725 1.975l-1.439 1.24a1.67 1.67 0 0 0-.572 1.406l.812 9.393A1.666 1.666 0 0 0 8.597 22h4.648c3.482 0 6.453-2.426 7.025-5.735"
                                />
                                <path
                                    fill="currentColor"
                                    fillRule="evenodd"
                                    d="M2.968 9.485a.75.75 0 0 1 .78.685l.97 11.236a1.237 1.237 0 1 1-2.468.107V10.234a.75.75 0 0 1 .718-.749"
                                    clipRule="evenodd"
                                />
                              </svg>
                              (0)
                            </span>
                <span className="details_rev_like d-flex align-items-center">
                              <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    d="m20.27 8.485l.705 4.08a1.666 1.666 0 0 1-1.64 1.95h-5.181a.833.833 0 0 0-.822.969l.663 4.045a4.8 4.8 0 0 1-.09 1.974c-.139.533-.55.962-1.092 1.136l-.145.047c-.328.105-.685.08-.994-.068a1.26 1.26 0 0 1-.68-.818l-.476-1.834a7.6 7.6 0 0 0-.656-1.679c-.415-.777-1.057-1.4-1.725-1.975l-1.439-1.24a1.67 1.67 0 0 1-.572-1.406l.812-9.393A1.666 1.666 0 0 1 8.597 2.75h4.648c3.482 0 6.453 2.426 7.025 5.735"
                                />
                                <path
                                    fill="currentColor"
                                    fillRule="evenodd"
                                    d="M2.968 15.265a.75.75 0 0 0 .78-.685l.97-11.236a1.237 1.237 0 1 0-2.468-.107v11.279a.75.75 0 0 0 .718.75"
                                    clipRule="evenodd"
                                />
                              </svg>
                              (0)
                            </span>
            </div>

            <div className="d-flex gap-2 align-items-start">
                <div className=" position-relative">
                    <img
                        src={window.origin + "/assets/images/icons/verify.svg"}
                        alt=""
                        className="details_profile_verify position-absolute"
                    />
                    <img
                        src={userAvatar || window.origin + "/assets/images/user.png"}
                        alt=""
                        className="details_profile_img"
                    />
                </div>
                <div className="col">
                    <p className="details_rev_name">{userName}</p>
                    <p className="details_rev_date">{convertToDays(createdAt)}</p>
                </div>
            </div>

            {sellerResponse && <div className="col px-md-4 mt-3">
                <div className="col d-flex align-items-center gap-2">
                    <p className="details_rev_head text-nowrap">
                        seller’s response
                    </p>
                    <hr className="details_rev_line"/>
                </div>
                <div className="d-flex gap-2">
                    <div className=" position-relative">
                        <img
                            src={window.origin + "/assets/images/icons/verify.svg"}
                            alt=""
                            className="details_profile_verify position-absolute"
                        />
                        <img
                            src={sellerAvatar || window.origin + "/assets/images/user.png"}
                            alt=""
                            className="details_profile_img"
                        />
                    </div>
                    <div className="col">
                        <div className="d-flex gap-3 align-items-center mb-2">
                            <p className="details_rev_name">{sellerName}</p>
                            <span className="details_profile_tag position-relative d-flex align-items-center">
                                    <svg
                                        className="position-absolute"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="1em"
                                        height="1em"
                                        viewBox="0 0 24 24">
                                      <path
                                          fill="currentColor"
                                          d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2L9.19 8.63L2 9.24l5.46 4.73L5.82 21z"
                                      />
                                    </svg>
                                    4.9
                                  </span>
                            <span className="details_profile_tag seller position-relative d-flex align-items-center">
                                    <svg
                                        className="position-absolute"
                                        xmlns="http://www.w3.org/2000/svg"
                                        width="1em"
                                        height="1em"
                                        viewBox="0 0 256 256">
                                      <path
                                          fill="currentColor"
                                          d="M239.75 90.81c0 .11 0 .21-.07.32L217 195a16 16 0 0 1-15.72 13H54.71A16 16 0 0 1 39 195L16.32 91.13c0-.11-.05-.21-.07-.32A16 16 0 0 1 44 77.39l33.67 36.29l35.8-80.29a1 1 0 0 0 0-.1a16 16 0 0 1 29.06 0a1 1 0 0 0 0 .1l35.8 80.29L212 77.39a16 16 0 0 1 27.71 13.42Z"
                                      />
                                    </svg>
                                    Top seller
                                  </span>
                        </div>
                        <p className="details_rev_text mb-3">
                            {sellerResponse}
                        </p>
                    </div>
                </div>
            </div>}
        </div>
    )
}