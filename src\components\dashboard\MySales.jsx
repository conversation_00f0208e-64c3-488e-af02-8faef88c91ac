import Spinner from "../common/Spinner.jsx";
import SaleRow from "../sales/SaleRow.jsx";
import Pagination from "../../pages/componets/utility/Pagination.jsx";
import {useSales} from "../../hooks/sales/useSales.js";
import {generateQuery} from "../../vbrae-utils/lib/misc.js";
import {getUserId} from "../../vbrae-utils/index.js";
import {useState} from "react";
import SaleMobileRow from "../sales/SaleMobileRow.jsx";

export default function MySales(){

    const userId = getUserId();
    const [page, setPage] = useState(1);

    const {salesData, salesLoading} = useSales({query: generateQuery({
            page,
            userId,
            staticString: `active=true&userId=${userId}&limit=12`,
        })});

    if(!salesData || salesLoading) return <Spinner />;

    return (
        <>
            <hr className="acct_fil_line mb-0"/>
            {/* Mobile Table */}
            <div className="col d-lg-none mb-4">
                {salesData.orders.map(item => <SaleMobileRow key={item._id} {...item} />)}
            </div>
            {/* Desktop Table */}
            <div className="table-responsive d-none d-lg-block">
                <table className="table table-borderless acct_table mb-3">
                    <thead>
                    <tr>
                        <th className="acct_table_head">Name</th>
                        <th className="acct_table_head d-flex align-items-center gap-1">
                            IWTR
                            <span className="icon">
                            <svg
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="currentColor"
                                xmlns="http://www.w3.org/2000/svg">
                              <path
                                  d="M3 5V6.66667H7.66667V5H3ZM3 9.16667V10.8333H12.3333V9.16667H3ZM3 13.3333V15H17V13.3333H3Z"/>
                            </svg>
                          </span>
                        </th>
                        <th className="acct_table_head col-1">BID</th>
                        <th className="acct_table_head">ORDER INCREMENT ID</th>
                        <th className="acct_table_head">RESERVATION ID</th>
                        <th className="acct_table_head">STATUS</th>
                        <th className="acct_table_head">CREATED</th>
                        <th className="acct_table_head">RELEASED</th>
                        <th className="acct_table_head"></th>
                    </tr>
                    </thead>
                    <tbody>
                    {salesData.orders.map(item => <SaleRow {...item} key={item._id}/>)}
                    </tbody>
                </table>
            </div>
            {/*{!!salesData.pagination.pages && <div className="col d-flex justify-content-center">*/}
            {/*    <Pagination*/}
            {/*        totalPages={salesData.pagination.pages}*/}
            {/*        currentPage={page}*/}
            {/*        pageClick={(page) => setPage(page)}*/}
            {/*        nextPage={() => setPage(page + 1)}*/}
            {/*        prevPage={() => setPage(page - 1)}/>*/}
            {/*</div>}*/}
            <div className="col d-flex justify-content-center">
                <Pagination/>
            </div>
        </>
    )
}