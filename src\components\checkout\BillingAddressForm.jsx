import useBillingForm from "../../hooks/checkout/useBillingForm.js";
import {useProfile} from "../../hooks/auth/useProfile.js";

export default function BillingAddressForm(){
    const {user} = useProfile();
    const {formik} = useBillingForm({existingAddress: user?.address});
    return (
        <form onSubmit={formik.handleSubmit}>
            <div className="col">
                <div className="col mb-3">
                    <label
                        htmlFor="street"
                        className="auth_label text-uppercase mb-2">
                        Street
                    </label>
                    <input
                        type="text"
                        name="street"
                        className="auth_input"
                        {...formik.getFieldProps('street')}
                    />
                    {formik.touched.street && <small className="main_footer_copy text-danger">{formik.errors.street}</small>}
                </div>
                <div className="col mb-3">
                    <label
                        htmlFor="city"
                        className="auth_label text-uppercase mb-2">
                        City
                    </label>
                    <input
                        type="text"
                        name="city"
                        className="auth_input"
                        {...formik.getFieldProps('city')}
                    />
                    {formik.touched.city && <small className="main_footer_copy text-danger">{formik.errors.city}</small>}
                </div>
                <div className="col mb-3">
                    <label
                        htmlFor="street"
                        className="auth_label text-uppercase mb-2">
                        State
                    </label>
                    <input
                        type="text"
                        name="state"
                        className="auth_input"
                        {...formik.getFieldProps('state')}
                    />
                    {formik.touched.state && <small className="main_footer_copy text-danger">{formik.errors.state}</small>}
                </div>
                <div className="col mb-3">
                    <label
                        htmlFor="zip"
                        className="auth_label text-uppercase mb-2">
                        Zip
                    </label>
                    <input
                        type="text"
                        name="zip"
                        className="auth_input"
                        {...formik.getFieldProps('zip')}
                    />
                    {formik.touched.zip && <small className="main_footer_copy text-danger">{formik.errors.zip}</small>}
                </div>
                <div className="col mb-3">
                    <label
                        htmlFor="zip"
                        className="auth_label text-uppercase mb-2">
                        Country
                    </label>
                    <input
                        type="text"
                        name="country"
                        className="auth_input"
                        {...formik.getFieldProps('country')}
                    />
                    {formik.touched.country && <small className="main_footer_copy text-danger">{formik.errors.country}</small>}
                </div>
            </div>
            <button disabled={formik.isSubmitting || !formik.isValid}
                    className={`col-12 col-md-auto auth_btn ${formik.isValid ? 'active' : ''}`}>
                {formik.isSubmitting ? 'Processing...' : 'Submit'}
            </button>
        </form>
    )
}