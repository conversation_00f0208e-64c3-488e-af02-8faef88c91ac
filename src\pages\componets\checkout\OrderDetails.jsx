import CustomAccordion from "../utility/CustomAccordion.jsx";
import {useCart} from "../../../hooks/cart/useCart.js";
import OrderItem from "./OrderItem.jsx";
import Spinner from "../../../components/common/Spinner.jsx";

export default function OrderDetails(){

    const {cartData, cartLoading} = useCart();

    if(!cartData || cartLoading) {
        return <Spinner />
    }

    return (
        <div className="cart_sum_con mb-3">
            <CustomAccordion
                isActive={true}
                trigger={(isOpen) => (
                    <p className="cart_ord_head d-flex gap-2 align-items-center mb-0">
                            <span className="">
                              <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1em"
                                  height="1em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="currentColor"
                                    fillRule="evenodd"
                                    d="M14.665 2.33a.75.75 0 0 1 1.006.335l2.201 4.402c1.353.104 2.202.37 2.75 1.047c.9 1.114.541 2.79-.177 6.143l-.429 2c-.487 2.273-.73 3.409-1.555 4.076S16.474 21 14.15 21h-4.3c-2.324 0-3.486 0-4.31-.667c-.826-.667-1.07-1.803-1.556-4.076l-.429-2c-.718-3.353-1.078-5.029-.177-6.143c.548-.678 1.397-.943 2.75-1.047l2.201-4.402a.75.75 0 0 1 1.342.67l-1.835 3.67Q8.559 7 9.422 7h5.156q.863-.001 1.586.005l-1.835-3.67a.75.75 0 0 1 .336-1.006M7.25 12a.75.75 0 0 1 .75-.75h8a.75.75 0 0 1 0 1.5H8a.75.75 0 0 1-.75-.75M10 14.25a.75.75 0 0 0 0 1.5h4a.75.75 0 0 0 0-1.5z"
                                    clipRule="evenodd"></path>
                              </svg>
                            </span>
                        Order details ({cartData.items.length} items)
                        <span
                            className={
                                "icon ms-auto " + (isOpen ? "" : "rotate_180")
                            }>
                              <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="1.2em"
                                  height="1.2em"
                                  viewBox="0 0 24 24">
                                <path
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    d="m2 15.999l10.173-9.824l9.824 10.173"
                                />
                              </svg>
                            </span>
                    </p>
                )}
                content={
                    <div className="col mt-4">
                        {cartData.items.map((item) => <OrderItem key={item.offer} {...item} cartId={cartData.cart} />)}
                    </div>
                }
            />
        </div>
    )
}