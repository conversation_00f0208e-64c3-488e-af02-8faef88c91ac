import Pusher from 'pusher-js';
import {getAccessToken} from "./accessToken.js";

let pusherClient = null;

export const getPusherClient = () => {
    const token = getAccessToken();

    if (pusherClient) {
        return pusherClient;
    }

    pusherClient = new Pusher('09b1adba58d8659c01d4', {
        cluster: 'ap2',
        encrypted: true,
        authEndpoint: `${import.meta.env.VITE_BASE_URI}pusher/auth`,
        auth: {
            headers: {
                Authorization: `Bearer ${token}`,
            },
        },
    });

    return pusherClient;
};