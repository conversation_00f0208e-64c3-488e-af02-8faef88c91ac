import {Link} from "react-router-dom";
import useLoginForm from "../../hooks/auth/useLoginForm.js";
import {useContext} from "react";
import {ModalContext} from "../../store/ModalContext.jsx";

export default function LoginForm(){

    const {formik} = useLoginForm();
    const { OpenModal } = useContext(ModalContext);

    return (
        <form onSubmit={formik.handleSubmit}>
            <div className="col">
                <div className="col mb-3">
                    <label
                        htmlFor="email"
                        className="auth_label text-uppercase mb-2">
                        Email
                    </label>
                    <input
                        type="email"
                        name="email"
                        className="auth_input"
                        {...formik.getFieldProps('email')}
                    />
                    {formik.touched.email && <small className="main_footer_copy text-danger">{formik.errors.email}</small>}
                </div>
                <div className="col mb-3">
                    <label
                        htmlFor="password"
                        className="auth_label text-uppercase mb-2">
                        Password
                    </label>
                    <input
                        type="password"
                        name="password"
                        className="auth_input"
                        {...formik.getFieldProps('password')}
                    />
                    {formik.touched.password && <small className="main_footer_copy text-danger">{formik.errors.password}</small>}
                </div>
            </div>

            <div className="col d-flex justify-content-end align-items-center gap-2 mb-4">
                <Link onClick={()=>OpenModal("forgot")}>
                    <p className="register_text">Forgot Password?</p>
                </Link>
            </div>

            <div className="col d-md-flex align-items-center gap-4">
                <button disabled={formik.isSubmitting || !formik.isValid} className={`col-12 col-md-auto auth_btn ${formik.isValid ? 'active' : ''}`}>
                    {formik.isSubmitting ? 'Processing...' : 'Login'}
                </button>
                <p className="register_text text-white mt-3 mt-md-0 mb-0">
                    Don&apos;t have an account?{" "}
                    <Link className="text-decoration-none" onClick={()=>OpenModal('register')}>Register</Link>
                </p>
            </div>
        </form>
    )
}