/* eslint-disable react/prop-types */

import useReplyForm from "../../../hooks/rating/useReplyForm.js";

export default function ReplyModal({ onClose, data}) {

    const {formik} = useReplyForm({data, onClose});

    return (
        <>
            {data.isOpen && (
                <form onSubmit={formik.handleSubmit} className="modal_con d-md-flex justify-content-center align-items-center inset-0">
                    <div className="col col-lg-10 col-xl-6 modal_cont d-flex flex-column position-relative">
                        <span
                            className="dash_level_close position-absolute"
                            role="button"
                            onClick={onClose}>

                        </span>
                        <p className="acct_head_smm">Add reply</p>
                        {/* Rating */}
                        <div className="col d-">
                            <p className="acct_offer_title mb-4">
                                {data.comment}
                            </p>

                            <div className="col mb-3">
                                <p className="vb_head mb-2">Reply</p>
                                <textarea
                                    className="vb_contact_input h-auto"
                                    id="content"
                                    cols="30"
                                    rows="5"
                                    {...formik.getFieldProps("sellerResponse")}
                                />
                                {formik.touched.sellerResponse &&
                                    <small className="main_footer_copy text-danger">{formik.errors.sellerResponse}</small>}
                            </div>
                        </div>

                        {/* Thank You */}
                        <div className="col d-none">
                            <p className="acct_offer_title mb-2">Thank You</p>
                            <p className="acct_offer_text">
                                We credit the review balance to your wallet.
                            </p>
                        </div>

                        <div className="d-flex gap-3 mt-5">
                            <button type="button" className="acct_offer_btn1" onClick={onClose}>
                                Cancel
                            </button>
                            <button className="acct_offer_btn3" disabled={formik.isSubmitting}>
                                {formik.isSubmitting ? "Processing..." : "Next"}
                            </button>
                        </div>
                    </div>
                </form>
            )}
        </>
    );
}
