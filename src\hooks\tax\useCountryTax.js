import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getTaxByCountry} from "../../api/tax/getTaxByCountry.js";
import {useProfile} from "../auth/useProfile.js";

export const useCountryTax = () => {

    const {user} = useProfile();

    const { data:countryTax } = useQueryFix({
        query: useQuery({
            queryKey: ['country-tax'],
            queryFn: () => getTaxByCountry({country: user.country}),
            onError: showError,
            onSuccess: ()=>{},
            enabled: !!user,
            refetchOnWindowFocus: false,
        }),
        transform: (response) => response.taxPercentage,
    });

    return { countryTax };
};