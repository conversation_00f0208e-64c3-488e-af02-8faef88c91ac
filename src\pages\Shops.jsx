import {Link} from "react-router-dom";
import Pagination from "./componets/utility/Pagination";
import MainHeader from "./componets/MainHeader";
import MenuSideBar from "./componets/MenuSideBar";
import {useEffect, useState} from "react";
import RightSideBar from "./componets/RightSideBar";
import MainFooter from "./componets/MainFooter";
import {useShops} from "../hooks/shop/useShops.js";
import Spinner from "../components/common/Spinner.jsx";
import ShopItem from "./componets/shop/ShopItem.jsx";
import EmptyGameItem from "./componets/EmptyGameItem.jsx";

export default function Shops() {

  return (
    <>
      <div className="d-flex main_house_con">
        <MenuSideBar makeShort={true} activeLink={"shops"} />

        <div
          className="col housing_con d-flex flex-column"
          style={
            {
              // backgroundImage: " url('./assets/images/psn_bg.png')",
            }
          }>
          <MainHeader activeLink={"shops"} />

          <div className="housing d-flex gap-1 position-relative">
            <div id="scrollable-section" className="col main_section">
              <div className="d-flex align-items-center gap-2 mb-3">
                <Link to={"/"}>
                  <span className="crumb_icon">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="1em"
                      height="1em"
                      viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M13.45 2.533a2.25 2.25 0 0 0-2.9 0L3.8 8.228a2.25 2.25 0 0 0-.8 1.72v9.305c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V15.25c0-.68.542-1.232 1.217-1.25h2.566a1.25 1.25 0 0 1 1.217 1.25v4.003c0 .966.784 1.75 1.75 1.75h3a1.75 1.75 0 0 0 1.75-1.75V9.947a2.25 2.25 0 0 0-.8-1.72z"
                      />
                    </svg>
                  </span>
                </Link>
                <Link>
                  <p className="crumb_link">/ Shops</p>
                </Link>
              </div>

              <RenderShops />

              <div className="col d-lg-none">
                <MainFooter />
              </div>
            </div>

            <RightSideBar />
          </div>
        </div>
      </div>
    </>
  );
}


const RenderShops = () => {

  const {shops, shopsLoading} = useShops();
  const [page, setPage] = useState(1)
  const [filledCards, setFilledCards] = useState([]);

  useEffect(() => {

    if(!shops) return;

    fillGrid();
    window.addEventListener("resize", fillGrid); // Refill on resize
    return () => window.removeEventListener("resize", fillGrid);
  }, [shops]);

  const fillGrid = () => {
    const container = document.querySelector(".game_list_con");
    const containerWidth = container.clientWidth; // Get container width
    const itemsPerRow = Math.floor(containerWidth / 250); // Calculate items that can fit

    const totalItems = Math.ceil(shops.data.length / itemsPerRow) * itemsPerRow; // Calculate total items needed
    const placeholders = totalItems - shops.data.length; // Calculate number of placeholders needed
    const filled = [...shops.data, ...Array(placeholders).fill(null)]; // Create filled array

    setFilledCards(filled);
  };

  if(!shops || shopsLoading){
    return <Spinner />
  }

  return (
      <div className="main_cont d-flex gap-4 position-relative">

        <div className="w-100">
          <p className="cate_filter_head mb-2">34 ITEMS</p>

          <h3 className="cate_header">Shops</h3>
          <hr className="cate_hr my-4"/>

          <div className="col d-xl-flex flex-row-reverse align-items-start align-items-xl-center gap-3 gap-xl-4 mb-4">
            <div className="col col-xl-3 d-flex gap-2 align-items-center mb-3 mb-xl-0">
              <select className="col col-lg-6 col-xl cate_filter_sort_sel">
                <option value="">Most recent</option>
              </select>
            </div>
          </div>

          <div className="col game_list_con row justify-content-start g-3 mb-4">
            {filledCards.map((card, index) => (
                <>
                  {card !== null ? (
                      <ShopItem key={index} {...card} />
                  ) : (
                      <EmptyGameItem />
                  )}
                </>
            ))}
          </div>

          <div className="col d-flex justify-content-center">
            {shops.pagination.pages && <Pagination
                totalPages={shops.pagination.pages}
                currentPage={page}
                pageClick={(page) => setPage(page)}
                nextPage={() => setPage(page + 1)}
                prevPage={() => setPage(page - 1)}
            />}
          </div>
        </div>
      </div>
  )
}