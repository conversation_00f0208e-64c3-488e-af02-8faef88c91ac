import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getTickets} from "../../api/ticket/getTickets.js";

export const useTickets = ({query, page = 1, limit = 10, status = 'open'}) => {
    // Build query string with default parameters
    const queryString = query || `page=${page}&limit=${limit}&status=${status}`;

    console.log('🎫 useTickets hook called with:', { query, page, limit, status });
    console.log('🎫 Final query string:', queryString);

    const { data: tickets, loading: ticketsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['tickets', queryString],
            queryFn: () => getTickets(queryString),
            onError: (error) => {
                console.error('🎫 Tickets API Error:', error);
                showError(error);
            },
            onSuccess: (data) => {
                console.log('🎫 useTickets onSuccess - Raw data:', data);
                console.log('🎫 useTickets onSuccess - Data array:', data?.data);
                console.log('🎫 useTickets onSuccess - Pagination:', data?.pagination);
            },
            refetchOnWindowFocus: false,
        }),
        transform: (data) => {
            console.log('🎫 useTickets transform - Input data:', data);
            console.log('🎫 useTickets transform - Output data:', data);
            return data;
        },
    });

    console.log('🎫 useTickets hook returning:', { tickets, ticketsLoading });
    console.log('🎫 Final tickets data:', tickets);

    return { tickets, ticketsLoading};
};