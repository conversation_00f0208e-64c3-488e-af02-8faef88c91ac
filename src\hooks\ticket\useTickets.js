import {useQuery} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {getTickets} from "../../api/ticket/getTickets.js";

export const useTickets = ({query, page = 1, limit = 10, status = 'open'}) => {
    // Build query string with default parameters
    const queryString = query || `page=${page}&limit=${limit}&status=${status}`;

    const { data: tickets, loading: ticketsLoading } = useQueryFix({
        query: useQuery({
            queryKey: ['tickets', queryString],
            queryFn: () => getTickets(queryString),
            onError: showError,
            refetchOnWindowFocus: false,
        }),
        transform: (data) => data,
    });

    return { tickets, ticketsLoading};
};