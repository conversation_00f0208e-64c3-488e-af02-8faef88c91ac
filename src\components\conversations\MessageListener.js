import {useEffect} from 'react';
import {useQueryClient} from "react-query";
import {getPusherClient} from "../../vbrae-utils/lib/pusher.js";
import {useSearchParams} from "react-router-dom";

export const MessageListener = () => {

    const [searchParams] = useSearchParams();
    const conversationId = searchParams.get("conversationId");

    const queryClient = useQueryClient();

    useEffect(() => {
        if(!conversationId) return;
        const pusherClient = getPusherClient();
        const channel = pusherClient.subscribe(`conversation-${conversationId}`);
        channel.bind('new-message', (newMessage) => {
            queryClient.setQueryData(['messages', conversationId], (oldMessages) => ({
                ...(oldMessages ?? { messages: [] }),
                messages: [...(oldMessages?.messages ?? []), newMessage],
            }));
        });

        return () => {
            channel.unbind_all();
            channel.unsubscribe();
        };
    }, [conversationId]);

    return null;
};