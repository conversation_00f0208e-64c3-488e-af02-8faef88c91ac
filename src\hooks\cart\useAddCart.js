import {useQuery, useQueryClient} from "react-query";
import {showError, useQueryFix} from "../../vbrae-utils/index.js";
import {addToCart} from "../../api/cart/addToCart.js";

export const useAddCart = (props) => {

    const queryClient = useQueryClient();

    const { loading: cartLoading, refetch: cartRefetch } = useQueryFix({
        query: useQuery({
            queryKey: ['add-cart', props.offerId],
            queryFn: () => addToCart(props),
            onError: showError,
            refetchOnWindowFocus: false,
            onSuccess: ()=> {
                queryClient.invalidateQueries(['game-item']);
                queryClient.invalidateQueries(['get-cart']).finally()
            },
            enabled: false
        }),
        transform: (data) => data,
    });

    return { cartRefetch, cartLoading };
};