import {Link} from "react-router-dom";

const genreList = [
    { _id: 1, url: "/genre/action", title: "Action", image: "./assets/images/icons/cate_action.svg" },
    { _id: 2, url: "/genre/adventure", title: "Adventure", image: "./assets/images/icons/cate_adventure.svg" },
    { _id: 3, url: "/genre/fps-tps", title: "FPS / TPS", image: "./assets/images/icons/cate_fps.svg" },
    { _id: 4, url: "/genre/rpg", title: "RPG", image: "./assets/images/icons/cate_rpg.svg" },
    { _id: 5, url: "/genre/indie", title: "Indie", image: "./assets/images/icons/cate_indie.svg" },
    { _id: 6, url: "/genre/simulation", title: "Simulation", image: "./assets/images/icons/cate_simulation.svg" },
    { _id: 7, url: "/genre/strategy", title: "Strategy", image: "./assets/images/icons/cate_strategy.svg" },
    { _id: 8, url: "/genre/racing", title: "Racing", image: "./assets/images/icons/cate_race.svg" },
];

export default function Genres() {
    return (
        <div className="cate_con mb-5">
            <div className="d-flex mb-4">
                <h3 className="main_title mx-auto">Genres</h3>
            </div>

            <div className="d-flex flex-wrap justify-content-center">
                {genreList.map((genre) => (
                    <div key={genre._id} className="col-auto px-2 mb-3">
                        <Link to={genre.url}>
                            <div className="col cate_item d-flex flex-column text-center">
                                <img
                                    src={genre.image}
                                    alt={genre.title}
                                    className="cate_item_img my-auto"
                                />
                                <p className="cate_item_text">{genre.title}</p>
                            </div>
                        </Link>
                    </div>
                ))}
            </div>
        </div>
    );
}
