import OrderAccordion from "../utility/OrderAccordion";
import useRetailPrice from "../../../hooks/offers/useRetailPrice.js";
import {useOfferDetails} from "../../../hooks/offers/useOfferDetails.js";
import Spinner from "../../../components/common/Spinner.jsx";

/* eslint-disable react/prop-types */
export default function SetRetailPrice({ isOpen, onClose }) {

  const {offer, offerLoading} = useOfferDetails({_id: isOpen._id})
  const {formik} = useRetailPrice({...isOpen, onClose});

  return (
    <>
      {isOpen.value && (
        <div className="modal_con d-lg-flex justify-content-center align-items-center inset-0">
          <div className="col col-lg-10 col-xl-6 modal_cont position-relative">
            <span
              className="dash_level_close position-absolute"
              onClick={() => onClose()}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="1em"
                height="1em"
                viewBox="0 0 24 24">
                <path
                  fill="currentColor"
                  d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12z"
                />
              </svg>
            </span>
            <p className="dash_level_title mb-4">Set the Retail Price</p>

            {!offer || offerLoading ? <Spinner /> : (
                <div className="col-12 col-md-8 d-flex align-items-center gap-3 mb-3">
                  <img
                      src={offer.data.template.coverImage}
                      alt=""
                      className="acct_sale_img"
                  />
                  <div className="col">
                    <p className="acct_head_s mb-2">
                      {offer.data.name}
                    </p>
                    <div className="col">
                      <p className="acct_head_sm mb-1">Current Best Price</p>
                      <p className="acct_offer_text_big text-white">$179.8</p>
                    </div>
                  </div>
                </div>
            )}

            <div className="d-md-flex gap-3">
              <div className="col mb-3">
                <label
                    htmlFor="expectedPrice"
                    className="auth_label text-uppercase mb-2">
                  I Want To Receive (Commission Rate: 10%)
                </label>
                <input
                    type="number"
                    id="expectedPrice"
                    className="auth_input"
                    placeholder="$ 179.8"
                    min={1}
                    {...formik.getFieldProps("expectedPrice")}
                    onChange={e => {
                      const value = parseFloat(e.target.value);
                    formik.setFieldValue("expectedPrice", value);
                    formik.setFieldValue("customerPays", (value + value * 0.1).toFixed(2));
                  }}
                />
                {formik.touched.expectedPrice && <small className="main_footer_copy text-danger">{formik.errors.expectedPrice}</small>}
              </div>
              <div className="col mb-3">
                <label
                  htmlFor="customerPays"
                  className="auth_label text-uppercase mb-2">
                  Customer Pay
                </label>
                <input
                  type="text"
                  id="customerPays"
                  className="auth_input"
                  placeholder="$ 179.8"
                  min={1}
                  {...formik.getFieldProps("customerPays")}
                  onChange={e=> {
                    const value = parseFloat(e.target.value);
                    formik.setFieldValue("customerPays", value);
                    formik.setFieldValue("expectedPrice", value - (value * 0.1));
                  }}
                />
                {formik.touched.customerPays && <small className="main_footer_copy text-danger">{formik.errors.customerPays}</small>}
              </div>
            </div>

            <div className="col mb-4">
              <OrderAccordion
                isActive={false}
                trigger={({ isOpen, ToggleAccordion }) => (
                  <div className="col d-flex flex-wrap gap-3 justify-content-between align-items-center mb-3">
                    <p className="acct_offer_title">Other Offers</p>
                    <div
                      className="acct_offer_arrow d-flex gap-1 justify-content-center align-items-center"
                      role="button"
                      onClick={() => ToggleAccordion()}>
                      {isOpen.value ? "Close" : "Expand"}
                      <span className="icon">
                        {isOpen.value ? (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                            <path
                              fill="none"
                              stroke="currentColor"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="m5 15l7-7l7 7"></path>
                          </svg>
                        ) : (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="1em"
                            height="1em"
                            viewBox="0 0 24 24">
                            <path
                              fill="none"
                              stroke="currentColor"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="m19 9l-7 7l-7-7"></path>
                          </svg>
                        )}
                      </span>
                    </div>
                  </div>
                )}
                content={
                  <>
                    <table className="table table-borderless acct_table">
                      <thead>
                        <tr>
                          <th className="acct_table_head">#</th>
                          <th className="acct_table_head">Seller Name</th>
                          <th className="acct_table_head">Price</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr className="acct_table_row mb-2">
                          <td className="acct_table_data">1</td>
                          <td className="acct_table_data">GG-Market</td>
                          <td className="acct_table_data">€0.08</td>
                        </tr>
                        <tr className="acct_table_row mb-2">
                          <td className="acct_table_data">2</td>
                          <td className="acct_table_data">GG-Market</td>
                          <td className="acct_table_data">€0.08</td>
                        </tr>
                        <tr className="acct_table_row mb-2">
                          <td className="acct_table_data">3</td>
                          <td className="acct_table_data">GG-Market</td>
                          <td className="acct_table_data">€0.08</td>
                        </tr>
                        <tr className="acct_table_row mb-2">
                          <td className="acct_table_data">4</td>
                          <td className="acct_table_data">GG-Market</td>
                          <td className="acct_table_data">€0.08</td>
                        </tr>
                        <tr className="acct_table_row mb-2">
                          <td className="acct_table_data">5</td>
                          <td className="acct_table_data">GG-Market</td>
                          <td className="acct_table_data">€0.08</td>
                        </tr>
                      </tbody>
                    </table>
                  </>
                }
              />
            </div>

            <div className="d-flex">
              <button type="button" className="col col-lg-3 main_button" onClick={formik.handleSubmit} disabled={formik.isSubmitting}>
                {formik.isSubmitting ? "Saving..." : "Save"}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
