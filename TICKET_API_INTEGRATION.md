# Ticket API Integration Documentation

## Overview
This document describes the integration of the new ticket API endpoints into the VBRAE application.

## API Endpoints Integrated

### 1. Get User's Tickets
```javascript
// Endpoint: GET /tickets/my-tickets
// Query Parameters: page, limit, status, search, sortBy, sortOrder

const tickets = await fetch('/tickets/my-tickets?page=1&limit=10&status=open', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### 2. Get Single Ticket
```javascript
// Endpoint: GET /tickets/{ticketId}

const ticket = await fetch(`/tickets/${ticketId}`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

## Files Modified/Created

### 1. API Layer (`src/api/ticket/`)

#### `getTickets.js` - Updated
- **Before**: Used generic `tickets?${query}` endpoint
- **After**: Uses `/tickets/my-tickets` with proper query parameters
- **Features**: 
  - Default parameters (page=1, limit=10, status=open)
  - Proper query string handling

#### `getTicketDetails.js` - Updated
- **Before**: Already used correct endpoint `/tickets/${_id}`
- **After**: Added comments for clarity
- **Features**: Direct ticket ID lookup

### 2. Hooks Layer (`src/hooks/ticket/`)

#### `useTickets.js` - Enhanced
- **New Parameters**: `page`, `limit`, `status` with defaults
- **Features**:
  - Flexible query building
  - Default pagination (page=1, limit=10)
  - Default status filtering (status='open')
  - Backward compatibility with existing query parameter

#### `useTicketDetails.js` - No changes needed
- Already properly implemented for single ticket fetching

### 3. Utility Functions (`src/vbrae-utils/lib/misc.js`)

#### `generateTicketQuery()` - New Function
```javascript
export const generateTicketQuery = ({
    page = 1,
    limit = 10,
    status = 'open',
    search,
    sortBy,
    sortOrder = 'desc'
}) => {
    // Returns properly formatted query string
}
```

**Features**:
- URL encoding for search terms
- Default values for common parameters
- Flexible parameter handling

### 4. UI Components

#### `Support.jsx` - Enhanced
- **New Features**:
  - Status filter dropdown (Open, Closed, Pending, All)
  - Improved search functionality
  - Uses new `generateTicketQuery` function

#### `TicketApiTest.jsx` - New Test Component
- **Purpose**: Testing and debugging API integration
- **Features**:
  - Live API testing interface
  - Real-time query string display
  - Response data visualization
  - Parameter adjustment controls

## Query Parameters Supported

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | number | 1 | Page number for pagination |
| `limit` | number | 10 | Number of tickets per page |
| `status` | string | 'open' | Filter by ticket status |
| `search` | string | - | Search in ticket content |
| `sortBy` | string | - | Field to sort by |
| `sortOrder` | string | 'desc' | Sort direction (asc/desc) |

## Usage Examples

### Basic Usage
```javascript
import { useTickets } from '../../hooks/ticket/useTickets.js';

// Get open tickets with defaults
const { tickets, ticketsLoading } = useTickets({});

// Get specific page
const { tickets, ticketsLoading } = useTickets({
  page: 2,
  limit: 20,
  status: 'closed'
});
```

### Advanced Usage with Search
```javascript
import { generateTicketQuery } from '../../vbrae-utils/lib/misc.js';

const query = generateTicketQuery({
  page: 1,
  limit: 15,
  status: 'open',
  search: 'payment issue',
  sortBy: 'createdAt',
  sortOrder: 'desc'
});

const { tickets, ticketsLoading } = useTickets({ query });
```

### Single Ticket Details
```javascript
import { useTicketDetails } from '../../hooks/ticket/useTicketDetails.js';

const { ticketDetails, ticketDetailsLoading } = useTicketDetails({
  _id: 'ticket-id-here'
});
```

## Authentication
All ticket API calls automatically include the Bearer token from localStorage:
- Token is retrieved using `getAccessToken()` from `vbrae-utils`
- Added to Authorization header: `Bearer ${token}`
- Handled automatically by the `fetchExtra` utility

## Error Handling
- Network errors are handled by `showError` utility
- 401 responses automatically clear the access token
- React Query provides automatic retry logic
- Loading states are provided for all API calls

## Testing
1. Navigate to `/account/support` in the application
2. The `TicketApiTest` component will be visible at the top
3. Test different parameters and observe API calls
4. Check browser network tab for actual HTTP requests
5. Verify response data structure

## Migration Notes
- Existing code using `useTickets` will continue to work
- New status filtering and pagination features are opt-in
- The API test component can be removed after testing is complete

## Next Steps
1. Test with real API endpoints
2. Handle pagination in UI components
3. Add error states for failed API calls
4. Implement ticket creation/update flows
5. Remove test component after verification
