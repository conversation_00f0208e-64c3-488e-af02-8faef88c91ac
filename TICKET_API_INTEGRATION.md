# Ticket API Integration Documentation

## Overview
This document describes the integration of the new ticket API endpoints into the VBRAE application.

## API Endpoints Integrated

### 1. Get User's Tickets
```javascript
// Endpoint: GET /tickets/my-tickets
// Query Parameters: page, limit, status, search, sortBy, sortOrder

const tickets = await fetch('/tickets/my-tickets?page=1&limit=10&status=open', {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

### 2. Get Single Ticket
```javascript
// Endpoint: GET /tickets/{ticketId}

const ticket = await fetch(`/tickets/${ticketId}`, {
  headers: { 'Authorization': `Bearer ${token}` }
});
```

## Files Modified/Created

### 1. API Layer (`src/api/ticket/`)

#### `getTickets.js` - Updated
- **Before**: Used generic `tickets?${query}` endpoint
- **After**: Uses `/tickets/my-tickets` with proper query parameters
- **Features**: 
  - Default parameters (page=1, limit=10, status=open)
  - Proper query string handling

#### `getTicketDetails.js` - Updated
- **Before**: Already used correct endpoint `/tickets/${_id}`
- **After**: Added comments for clarity
- **Features**: Direct ticket ID lookup

### 2. Hooks Layer (`src/hooks/ticket/`)

#### `useTickets.js` - Enhanced
- **New Parameters**: `page`, `limit`, `status` with defaults
- **Features**:
  - Flexible query building
  - Default pagination (page=1, limit=10)
  - Default status filtering (status='open')
  - Backward compatibility with existing query parameter

#### `useTicketDetails.js` - No changes needed
- Already properly implemented for single ticket fetching

### 3. Utility Functions (`src/vbrae-utils/lib/misc.js`)

#### `generateTicketQuery()` - New Function
```javascript
export const generateTicketQuery = ({
    page = 1,
    limit = 10,
    status = 'open',
    search,
    sortBy,
    sortOrder = 'desc'
}) => {
    // Returns properly formatted query string
}
```

**Features**:
- URL encoding for search terms
- Default values for common parameters
- Flexible parameter handling

### 4. UI Components

#### `Support.jsx` - Enhanced
- **New Features**:
  - Status filter dropdown (Open, Closed, Pending, All)
  - Improved search functionality
  - Uses new `generateTicketQuery` function

#### `TicketApiTest.jsx` - New Test Component
- **Purpose**: Testing and debugging API integration
- **Features**:
  - Live API testing interface
  - Real-time query string display
  - Response data visualization
  - Parameter adjustment controls

## Query Parameters Supported

| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| `page` | number | 1 | Page number for pagination |
| `limit` | number | 10 | Number of tickets per page |
| `status` | string | 'open' | Filter by ticket status |
| `search` | string | - | Search in ticket content |
| `sortBy` | string | - | Field to sort by |
| `sortOrder` | string | 'desc' | Sort direction (asc/desc) |

## Usage Examples

### Basic Usage
```javascript
import { useTickets } from '../../hooks/ticket/useTickets.js';

// Get open tickets with defaults
const { tickets, ticketsLoading } = useTickets({});

// Get specific page
const { tickets, ticketsLoading } = useTickets({
  page: 2,
  limit: 20,
  status: 'closed'
});
```

### Advanced Usage with Search
```javascript
import { generateTicketQuery } from '../../vbrae-utils/lib/misc.js';

const query = generateTicketQuery({
  page: 1,
  limit: 15,
  status: 'open',
  search: 'payment issue',
  sortBy: 'createdAt',
  sortOrder: 'desc'
});

const { tickets, ticketsLoading } = useTickets({ query });
```

### Single Ticket Details
```javascript
import { useTicketDetails } from '../../hooks/ticket/useTicketDetails.js';

const { ticketDetails, ticketDetailsLoading } = useTicketDetails({
  _id: 'ticket-id-here'
});
```

## Authentication
All ticket API calls automatically include the Bearer token from localStorage:
- Token is retrieved using `getAccessToken()` from `vbrae-utils`
- Added to Authorization header: `Bearer ${token}`
- Handled automatically by the `fetchExtra` utility

## Error Handling
- Network errors are handled by `showError` utility
- 401 responses automatically clear the access token
- React Query provides automatic retry logic
- Loading states are provided for all API calls

## Testing
✅ **COMPLETED** - API integration has been tested and verified working
- API calls to `/tickets/my-tickets` are successful
- Proper authentication headers are included
- Response structure matches expected format
- Pagination and filtering work correctly

## Migration Notes
- ✅ Existing code using `useTickets` continues to work
- ✅ New status filtering and pagination features implemented
- ✅ Test component has been removed
- ✅ Mobile and desktop UI components updated

## Completed Features
1. ✅ Updated API endpoints to use new ticket API
2. ✅ Enhanced pagination in UI components
3. ✅ Added proper error states for failed API calls
4. ✅ Implemented status filtering (Open, Closed, Pending, All)
5. ✅ Added empty states for no tickets
6. ✅ Created mobile-responsive ticket components
7. ✅ Removed test components after verification

## UI Components Updated
- ✅ `Support.jsx` - Main tickets list page
- ✅ `SupportRow.jsx` - Desktop table row component
- ✅ `MobileSupportRow.jsx` - Mobile ticket card component (new)
- ✅ `Support_Details.jsx` - Single ticket details page
