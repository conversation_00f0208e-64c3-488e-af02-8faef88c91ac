import useForgetForm from "../../hooks/auth/useForgetForm.js";

export default function ForgotPasswordForm(){

    const {formik} = useForgetForm()

    return (
        <form onSubmit={formik.handleSubmit}>
            <div className="col">
                <div className="col mb-3">
                    <label
                        htmlFor="email"
                        className="auth_label text-uppercase mb-2">
                        Email
                    </label>
                    <input
                        type="email"
                        name="email"
                        className="auth_input"
                        {...formik.getFieldProps('email')}
                    />
                    {formik.touched.email && <small className="main_footer_copy text-danger">{formik.errors.email}</small>}
                </div>
            </div>

            <div className="col d-md-flex align-items-center gap-4">
                <button disabled={formik.isSubmitting || !formik.isValid} className="col-12 col-md-auto auth_btn">
                    {formik.isSubmitting ? 'Processing...' : 'Reset Password'}
                </button>
            </div>
        </form>
    )
}