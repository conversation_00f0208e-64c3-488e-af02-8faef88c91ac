import {useConversations} from "../../hooks/conversation/useConversations.js";
import Spinner from "../common/Spinner.jsx";
import {useProfile} from "../../hooks/auth/useProfile.js";
import {useSearchParams} from "react-router-dom";
import {useState} from "react";

export default function AllConversations({ToggleSelected}) {

    const {user} = useProfile();
    const {conversations, conversationLoading} = useConversations();
    const [search, setSearch] = useState("");

    const [_, setSearchParams] = useSearchParams();

    if(conversationLoading || !user) return(
        <div style={{height: '50vh'}} className="d-flex justify-content-center align-items-center">
            <Spinner />
        </div>
    )

    if(!conversations) return null

    return (
        <>
            <div className="col pad mb-3">
                <div className="col acct_box d-flex">
                    <input
                        type="text"
                        className="input_box"
                        placeholder="Search..."
                        value={search}
                        onChange={e=> setSearch(e.target.value.toLowerCase())}
                    />
                    <span className="icon">
                          <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 24 24">
                            <path
                                fill="currentColor"
                                fillRule="evenodd"
                                d="M18.319 14.433A8.001 8.001 0 0 0 6.343 3.868a8 8 0 0 0 10.564 11.976l.043.045l4.242 4.243a1 1 0 1 0 1.415-1.415l-4.243-4.242zm-2.076-9.15a6 6 0 1 1-8.485 8.485a6 6 0 0 1 8.485-8.485"
                                clipRule="evenodd"
                            />
                          </svg>
                        </span>
                </div>
            </div>

            <div className="col msg_cont_con">
                {conversations.map(conversation => {
                    const unreadCount = user.role === "seller" ? conversation.unreadSeller : conversation.unreadClient;
                    const nameToShow =  conversation.client._id === user._id ? "seller" : "client";
                    if(!conversation[nameToShow].name.toLowerCase().includes(search)) return null;
                    return (
                        <div key={conversation._id}
                             onClick={() => {
                                 setSearchParams({ conversationId: conversation._id});
                                 ToggleSelected();
                             }}
                             className="msg_cont fill d-flex gap-2 align-items-center position-relative mb-3">
                            {unreadCount > 0 && <span className="msg_cont_num d-flex justify-content-center align-items-center position-absolute">
                          {unreadCount}
                        </span>}
                            <img
                                src={conversation[nameToShow].avatar ?? "../assets/images/user.png"}
                                alt=""
                                className="msg_cont_img"
                            />
                            <div className="col">
                                <p className="msg_cont_text">
                                    <span className="text-truncate">{conversation.lastMessage}</span>
                                    <div className="d-flex justify-content-between align-items-center">
                                        <p className="msg_cont_name">{conversation[nameToShow].name}</p>
                                        <p className="msg_cont_date">11:49 pm</p>
                                    </div>
                                </p>
                            </div>
                            <span className="msg_cont_icon">
                          <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="1em"
                              height="1em"
                              viewBox="0 0 24 24">
                            <g fill="none">
                              <path d="m12.593 23.258l-.011.002l-.071.035l-.02.004l-.014-.004l-.071-.035q-.016-.005-.024.005l-.004.01l-.017.428l.005.02l.01.013l.104.074l.015.004l.012-.004l.104-.074l.012-.016l.004-.017l-.017-.427q-.004-.016-.017-.018m.265-.113l-.013.002l-.185.093l-.01.01l-.003.011l.018.43l.005.012l.008.007l.201.093q.019.005.029-.008l.004-.014l-.034-.614q-.005-.018-.02-.022m-.715.002a.02.02 0 0 0-.027.006l-.006.014l-.034.614q.001.018.017.024l.015-.002l.201-.093l.01-.008l.004-.011l.017-.43l-.003-.012l-.01-.01z"></path>
                              <path
                                  fill="currentColor"
                                  d="M12 17a2 2 0 1 1 0 4a2 2 0 0 1 0-4m0-7a2 2 0 1 1 0 4a2 2 0 0 1 0-4m0-7a2 2 0 1 1 0 4a2 2 0 0 1 0-4"></path>
                            </g>
                          </svg>
                        </span>
                        </div>
                    )
                })}
            </div>
        </>
    )
}