import * as Yup from 'yup';
import {useMutation, useQueryClient} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {applyCoupon} from "../../api/cart/applyCoupon.js";

const couponSchema = Yup.object().shape({
    couponCode:Yup.string().trim()
        .required('Coupon is required'),
});

export default function useCouponForm(props) {
    const queryClient = useQueryClient();
    const initialValues = {
        couponCode: ''
    }

    const { mutateAsync } = useMutation(applyCoupon, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: couponSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync({...values, ...props});
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess(response.message);
                queryClient.invalidateQueries(['get-cart']).finally()
            }
        },
    });

    return { formik };
}
