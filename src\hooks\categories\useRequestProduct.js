import * as Yup from 'yup';
import {useMutation} from "react-query";
import {useFormik} from "formik";
import {showError, showSuccess} from "../../vbrae-utils/index.js";
import {postRequestForm} from "../../api/categories/postRequestForm.js";

const requestSchema = Yup.object().shape({
    title:Yup.string().trim().required('Title is required'),
    additionalInformation:Yup.string(),
    steamAppId:Yup.string(),
    region: Yup.string().required('Region is required'),
    language: Yup.array()
        .of(Yup.string().required('Each language must be a valid string'))
        .min(1, 'At least one language is required')
        .required('Languages are required'),
    category: Yup.string().required('Category is required'),
});

export default function useRequestProduct() {
    const initialValues = {
        title: '',
        additionalInformation: '',
        steamAppId: '',
        region: 'Global',
        category: '',
        language: []
    }

    const { mutateAsync } = useMutation(postRequestForm, {
        onError: (error)=> showError(error),
    });

    const formik = useFormik({
        initialValues,
        enableReinitialize: true,
        validationSchema: requestSchema,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            setSubmitting(true);
            const response = await mutateAsync(values);
            setSubmitting(false);
            resetForm();
            if (response) {
                showSuccess(response.message)
            }
        },
    });

    return { formik };
}
